<?php $__env->startSection('title', 'السيارات - موتور لاين'); ?>
<?php $__env->startSection('meta_description', 'تصفح مجموعتنا الواسعة من السيارات الجديدة والمستعملة'); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">السيارات</h1>

            <!-- عرض إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="alert alert-success">
                        <i class="fas fa-car me-2"></i>
                        تم العثور على <strong><?php echo e($cars->total()); ?></strong> سيارة متاحة
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-info">
                        <i class="fas fa-filter me-2"></i>
                        عدد الفلاتر المطبقة: <strong><?php echo e(count(array_filter($appliedFilters))); ?></strong>
                    </div>
                </div>
            </div>

            <!-- عرض الفلاتر المطبقة -->
            <?php if(count(array_filter($appliedFilters)) > 0): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">الفلاتر المطبقة</h5>
                </div>
                <div class="card-body">
                    <?php if(isset($appliedFilters['search_term']) && $appliedFilters['search_term']): ?>
                        <span class="badge bg-primary me-2">البحث: <?php echo e($appliedFilters['search_term']); ?></span>
                    <?php endif; ?>
                    <?php if(isset($appliedFilters['brand_ids']) && is_array($appliedFilters['brand_ids'])): ?>
                        <span class="badge bg-secondary me-2">الماركات: <?php echo e(count($appliedFilters['brand_ids'])); ?></span>
                    <?php endif; ?>
                    <?php if(isset($appliedFilters['min_price']) || isset($appliedFilters['max_price'])): ?>
                        <span class="badge bg-success me-2">نطاق السعر مطبق</span>
                    <?php endif; ?>
                    <a href="<?php echo e(route('site.cars.index')); ?>" class="btn btn-sm btn-outline-danger">
                        <i class="fas fa-times me-1"></i>إزالة جميع الفلاتر
                    </a>
                </div>
            </div>
            <?php endif; ?>

            <!-- عرض خيارات الفلاتر -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">فلترة النتائج</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('site.cars.index')); ?>">
                        <div class="row">
                            <!-- فلتر البحث -->
                            <div class="col-md-6 mb-3">
                                <label for="search_term" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search_term" name="search_term"
                                       value="<?php echo e($appliedFilters['search_term'] ?? ''); ?>"
                                       placeholder="ابحث في السيارات...">
                            </div>

                            <!-- فلتر الترتيب -->
                            <div class="col-md-6 mb-3">
                                <label for="sort_by" class="form-label">ترتيب حسب</label>
                                <select class="form-select" id="sort_by" name="sort_by">
                                    <option value="latest" <?php echo e(($appliedFilters['sort_by'] ?? 'latest') == 'latest' ? 'selected' : ''); ?>>الأحدث</option>
                                    <option value="price_asc" <?php echo e(($appliedFilters['sort_by'] ?? '') == 'price_asc' ? 'selected' : ''); ?>>السعر: من الأقل للأعلى</option>
                                    <option value="price_desc" <?php echo e(($appliedFilters['sort_by'] ?? '') == 'price_desc' ? 'selected' : ''); ?>>السعر: من الأعلى للأقل</option>
                                    <option value="featured" <?php echo e(($appliedFilters['sort_by'] ?? '') == 'featured' ? 'selected' : ''); ?>>المميزة أولاً</option>
                                </select>
                            </div>

                            <!-- فلتر الماركات -->
                            <?php if($filterOptions['brands']->count() > 0): ?>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الماركات</label>
                                <div class="max-height-200 overflow-auto border rounded p-2">
                                    <?php $__currentLoopData = $filterOptions['brands']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="brand_ids[]"
                                               value="<?php echo e($brand->id); ?>" id="brand_<?php echo e($brand->id); ?>"
                                               <?php echo e(in_array($brand->id, $appliedFilters['brand_ids'] ?? []) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="brand_<?php echo e($brand->id); ?>">
                                            <?php echo e($brand->name); ?> (<?php echo e($brand->available_cars_count); ?>)
                                        </label>
                                    </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- فلتر نطاق السعر -->
                            <?php if($filterOptions['price_range'] && $filterOptions['price_range']->min_price): ?>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">نطاق السعر</label>
                                <div class="row">
                                    <div class="col-6">
                                        <input type="number" class="form-control" name="min_price"
                                               placeholder="من <?php echo e(number_format($filterOptions['price_range']->min_price)); ?>"
                                               value="<?php echo e($appliedFilters['min_price'] ?? ''); ?>">
                                    </div>
                                    <div class="col-6">
                                        <input type="number" class="form-control" name="max_price"
                                               placeholder="إلى <?php echo e(number_format($filterOptions['price_range']->max_price)); ?>"
                                               value="<?php echo e($appliedFilters['max_price'] ?? ''); ?>">
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>تطبيق الفلاتر
                            </button>
                            <a href="<?php echo e(route('site.cars.index')); ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-2"></i>إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- عرض النتائج -->
            <?php if($cars->count() > 0): ?>
            <div class="row">
                <?php $__currentLoopData = $cars; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $car): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100">
                        <!-- صورة السيارة -->
                        <div class="position-relative">
                            <?php if($car->getFirstMediaUrl('car_main_image')): ?>
                                <img src="<?php echo e($car->getFirstMediaUrl('car_main_image', 'medium')); ?>"
                                     class="card-img-top" alt="<?php echo e($car->title); ?>" style="height: 200px; object-fit: cover;">
                            <?php elseif($car->getFirstMediaUrl('car_images')): ?>
                                <img src="<?php echo e($car->getFirstMediaUrl('car_images', 'medium')); ?>"
                                     class="card-img-top" alt="<?php echo e($car->title); ?>" style="height: 200px; object-fit: cover;">
                            <?php else: ?>
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <i class="fas fa-car fa-3x text-muted"></i>
                                </div>
                            <?php endif; ?>

                            <?php if($car->is_featured): ?>
                                <span class="badge bg-warning position-absolute top-0 start-0 m-2">مميزة</span>
                            <?php endif; ?>
                        </div>

                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title"><?php echo e($car->title); ?></h5>
                            <p class="card-text text-muted small">
                                <?php echo e($car->brand->name); ?> - <?php echo e($car->carModel->name); ?> - <?php echo e($car->manufacturingYear->year); ?>

                            </p>

                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <small class="text-muted">اللون</small><br>
                                    <span class="badge bg-secondary"><?php echo e($car->mainColor->name); ?></span>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">الوقود</small><br>
                                    <span class="badge bg-info"><?php echo e($car->fuelType->name); ?></span>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">ناقل الحركة</small><br>
                                    <span class="badge bg-success"><?php echo e($car->transmissionType->name); ?></span>
                                </div>
                            </div>

                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h4 class="text-primary mb-0"><?php echo e(number_format($car->price)); ?> <?php echo e($car->currency); ?></h4>
                                    <?php if($car->offer_price && $car->offer_price < $car->price): ?>
                                        <small class="text-decoration-line-through text-muted"><?php echo e(number_format($car->offer_price)); ?></small>
                                    <?php endif; ?>
                                </div>
                                <a href="<?php echo e(route('site.cars.show', $car->id)); ?>" class="btn btn-primary w-100">
                                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- الترقيم -->
            <div class="d-flex justify-content-center">
                <?php echo e($cars->links()); ?>

            </div>
            <?php else: ?>
            <div class="alert alert-warning text-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                لم يتم العثور على سيارات تطابق معايير البحث المحددة.
                <br>
                <a href="<?php echo e(route('site.cars.index')); ?>" class="btn btn-outline-primary mt-2">
                    <i class="fas fa-undo me-2"></i>عرض جميع السيارات
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.max-height-200 {
    max-height: 200px;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('site.layouts.site_layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Project\MotorLine_10\resources\views/site/cars/index.blade.php ENDPATH**/ ?>