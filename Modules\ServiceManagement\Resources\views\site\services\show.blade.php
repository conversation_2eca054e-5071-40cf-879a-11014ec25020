@extends('site.layouts.site_layout')

@section('title', $service->name . ' - خدماتنا - موتور لاين')

@section('meta_description', Str::limit($service->description ?? 'تفاصيل خدمة ' . $service->name, 160))

@section('content')
<div class="container py-5">
    {{-- مسار التنقل --}}
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ route('site.home') }}" class="text-decoration-none">
                    <i class="fas fa-home me-1"></i>
                    الرئيسية
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{{ route('site.services.index') }}" class="text-decoration-none">
                    خدماتنا
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                {{ $service->name }}
            </li>
        </ol>
    </nav>

    <div class="row">
        {{-- تفاصيل الخدمة --}}
        <div class="col-lg-8 mb-4">
            <div class="card brand-card shadow">
                {{-- صورة الخدمة --}}
                @if($service->main_image_url)
                    <img src="{{ $service->main_image_url }}"
                         class="card-img-top"
                         alt="{{ $service->name }}"
                         style="height: 300px; object-fit: cover;">
                @else
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                         style="height: 300px;">
                        <i class="fas fa-tools fa-4x text-muted"></i>
                    </div>
                @endif

                <div class="card-body">
                    {{-- فئة الخدمة --}}
                    @if($service->category)
                        <div class="mb-3">
                            <span class="badge brand-badge-secondary fs-6">
                                <i class="fas fa-tag me-1"></i>
                                {{ $service->category->name }}
                            </span>
                        </div>
                    @endif

                    {{-- اسم الخدمة --}}
                    <h1 class="card-title brand-text-primary mb-3">
                        {{ $service->name }}
                    </h1>

                    {{-- السعر --}}
                    <div class="mb-4">
                        <span class="h3 brand-text-primary fw-bold">
                            {{ $service->formatted_price }}
                        </span>
                    </div>

                    {{-- وصف الخدمة --}}
                    @if($service->description)
                        <div class="mb-4">
                            <h5 class="brand-text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                تفاصيل الخدمة
                            </h5>
                            <div class="text-muted lh-lg">
                                {!! nl2br(e($service->description)) !!}
                            </div>
                        </div>
                    @endif

                    {{-- أزرار العمل --}}
                    <div class="d-grid gap-2 d-md-flex">
                        <button type="button"
                                class="btn brand-btn-primary btn-lg flex-md-fill"
                                data-service-id="{{ $service->id }}"
                                data-service-name="{{ $service->name }}"
                                data-bs-toggle="modal"
                                data-bs-target="#serviceRequestModal">
                            <i class="fas fa-paper-plane me-2"></i>
                            اطلب الخدمة الآن
                        </button>
                        <a href="{{ route('site.services.index') }}"
                           class="btn brand-btn-outline-primary btn-lg flex-md-fill">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للخدمات
                        </a>
                    </div>
                </div>
            </div>
        </div>

        {{-- الشريط الجانبي --}}
        <div class="col-lg-4">
            {{-- معلومات سريعة --}}
            <div class="card brand-card shadow mb-4">
                <div class="card-header brand-bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-tag text-muted me-2"></i>
                            <strong>الفئة:</strong>
                            {{ $service->category->name ?? 'غير محدد' }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-money-bill-wave text-muted me-2"></i>
                            <strong>السعر:</strong>
                            <span class="brand-text-primary fw-bold">{{ $service->formatted_price }}</span>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-clock text-muted me-2"></i>
                            <strong>وقت الاستجابة:</strong>
                            خلال 24 ساعة
                        </li>
                        <li>
                            <i class="fas fa-shield-alt text-muted me-2"></i>
                            <strong>الضمان:</strong>
                            حسب نوع الخدمة
                        </li>
                    </ul>
                </div>
            </div>

            {{-- خدمات مشابهة --}}
            @if($relatedServices->count() > 0)
                <div class="card brand-card shadow">
                    <div class="card-header brand-bg-secondary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            خدمات مشابهة
                        </h5>
                    </div>
                    <div class="card-body">
                        @foreach($relatedServices as $relatedService)
                            <div class="d-flex align-items-center mb-3 {{ !$loop->last ? 'border-bottom pb-3' : '' }}">
                                @if($relatedService->main_image_url)
                                    <img src="{{ $relatedService->main_image_url }}"
                                         class="rounded me-3"
                                         alt="{{ $relatedService->name }}"
                                         style="width: 60px; height: 60px; object-fit: cover;">
                                @else
                                    <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-tools text-muted"></i>
                                    </div>
                                @endif
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="{{ route('site.services.show', $relatedService->id) }}"
                                           class="text-decoration-none brand-text-primary">
                                            {{ $relatedService->name }}
                                        </a>
                                    </h6>
                                    <small class="text-muted">{{ $relatedService->formatted_price }}</small>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

{{-- تضمين نموذج طلب الخدمة مع تمرير بيانات الخدمة --}}
@include('servicemanagement::site.services._request_form', ['service' => $service])
@endsection

{{-- ملاحظة: تم نقل منطق معالجة نموذج طلب الخدمة إلى المكون _request_form.blade.php --}}

@push('styles')
<style>
.breadcrumb-item + .breadcrumb-item::before {
    content: "‹";
    font-weight: bold;
}

.card-img-top {
    transition: transform 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.02);
}

.lh-lg {
    line-height: 1.8 !important;
}

@media (max-width: 768px) {
    .card-img-top {
        height: 200px !important;
    }

    .btn-lg {
        font-size: 1rem;
        padding: 0.75rem 1rem;
    }
}
</style>
@endpush
