<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// مسارات الموقع العام للعروض الترويجية
Route::group(['namespace' => 'Site'], function () {
    Route::get('/promotions', 'SitePromotionController@index')->name('site.promotions.index');
    Route::get('/promotions/{promotion}', 'SitePromotionController@show')->name('site.promotions.show');
});

// مسارات لوحة التحكم (سيتم إضافتها لاحقاً)
Route::prefix('promotionmanagement')->group(function() {
    Route::get('/', 'PromotionManagementController@index');
});
