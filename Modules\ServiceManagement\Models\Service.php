<?php

namespace Modules\ServiceManagement\Models;

use Modules\Core\Models\BaseModel;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

/**
 * Service Model
 *
 * يمثل هذا النموذج جدول الخدمات في النظام
 * بناءً على مواصفات TS-FR.md (DB-TBL-015)
 *
 * @property int $id
 * @property int|null $category_id معرف فئة الخدمة
 * @property string $name اسم الخدمة
 * @property string|null $description وصف الخدمة
 * @property decimal $price سعر الخدمة
 * @property string $currency العملة
 * @property bool $status حالة الخدمة (نشطة/غير نشطة)
 * @property \Illuminate\Support\Carbon|null $created_at تاريخ الإنشاء
 * @property \Illuminate\Support\Carbon|null $updated_at تاريخ التحديث
 * @property \Modules\ServiceManagement\Models\ServiceCategory|null $category علاقة فئة الخدمة
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\ServiceManagement\Models\ServiceRequest[] $serviceRequests علاقة طلبات الخدمة
 */
class Service extends BaseModel implements HasMedia
{
    use InteractsWithMedia;
    use HasTranslations;

    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي
     *
     * @var array
     */
    protected $fillable = [
        'category_id',
        'name',
        'description',
        'price',
        'currency',
        'status',
    ];

    /**
     * الخصائص التي يمكن ترجمتها
     *
     * @var array
     */
    public $translatable = [
        'name',
        'description',
    ];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة
     *
     * @var array
     */
    protected $casts = [
        'price' => 'decimal:2',
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * علاقة فئة الخدمة
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function category()
    {
        return $this->belongsTo(ServiceCategory::class, 'category_id');
    }

    /**
     * علاقة طلبات الخدمة
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function serviceRequests()
    {
        return $this->hasMany(ServiceRequest::class, 'service_id');
    }

    /**
     * تسجيل مجموعات الوسائط
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('service_images')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);
    }

    /**
     * نطاق للحصول على الخدمات النشطة فقط
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * نطاق للحصول على الخدمات مع فئاتها
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithCategory($query)
    {
        return $query->with('category');
    }

    /**
     * الحصول على صورة الخدمة الرئيسية
     *
     * @return string|null
     */
    public function getMainImageUrlAttribute()
    {
        $media = $this->getFirstMedia('service_images');
        return $media ? $media->getUrl() : null;
    }

    /**
     * الحصول على السعر مع العملة
     *
     * @return string
     */
    public function getFormattedPriceAttribute()
    {
        return number_format($this->price, 2) . ' ' . $this->currency;
    }

    /**
     * مصنع النموذج
     *
     * @return \Modules\ServiceManagement\Database\factories\ServiceFactory
     */
    protected static function newFactory()
    {
        return \Modules\ServiceManagement\Database\factories\ServiceFactory::new();
    }
}
