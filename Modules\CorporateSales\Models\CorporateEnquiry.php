<?php

namespace Modules\CorporateSales\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Core\Models\BaseModel;
use Modules\UserManagement\Models\User;

/**
 * CorporateEnquiry Model
 *
 * يمثل هذا النموذج جدول طلبات الشركات في النظام
 *
 * @property int $id
 * @property string $company_name اسم الشركة
 * @property string $contact_person_name اسم مسؤول الاتصال
 * @property string $email البريد الإلكتروني
 * @property string $phone رقم الجوال
 * @property string $request_details تفاصيل الطلب
 * @property string|null $city المدينة
 * @property string $status حالة الطلب
 * @property string|null $admin_notes ملاحظات الإدارة
 * @property int|null $assigned_employee_id معرف الموظف المعين
 * @property \Illuminate\Support\Carbon|null $contacted_at تاريخ أول اتصال
 * @property \Illuminate\Support\Carbon|null $follow_up_date تاريخ المتابعة المقرر
 * @property \Illuminate\Support\Carbon|null $created_at تاريخ الإنشاء
 * @property \Illuminate\Support\Carbon|null $updated_at تاريخ التحديث
 * @property \Illuminate\Support\Carbon|null $deleted_at تاريخ الحذف الناعم
 */
class CorporateEnquiry extends BaseModel
{
    use HasFactory, SoftDeletes;

    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي
     *
     * @var array
     */
    protected $fillable = [
        'company_name',
        'contact_person_name',
        'email',
        'phone',
        'request_details',
        'city',
        'status',
        'admin_notes',
        'assigned_employee_id',
        'contacted_at',
        'follow_up_date',
    ];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة
     *
     * @var array
     */
    protected $casts = [
        'contacted_at' => 'datetime',
        'follow_up_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * حالات الطلب المتاحة
     */
    const STATUS_PENDING = 'pending';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * الحصول على جميع حالات الطلب المتاحة
     *
     * @return array
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_PENDING => 'في الانتظار',
            self::STATUS_IN_PROGRESS => 'قيد المعالجة',
            self::STATUS_COMPLETED => 'مكتمل',
            self::STATUS_CANCELLED => 'ملغي',
        ];
    }

    /**
     * الحصول على نص حالة الطلب باللغة العربية
     *
     * @return string
     */
    public function getStatusTextAttribute(): string
    {
        return self::getStatuses()[$this->status] ?? 'غير محدد';
    }

    /**
     * الحصول على لون حالة الطلب للعرض
     *
     * @return string
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_IN_PROGRESS => 'info',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_CANCELLED => 'danger',
            default => 'secondary',
        };
    }

    /**
     * علاقة الموظف المعين للطلب
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function assignedEmployee()
    {
        return $this->belongsTo(User::class, 'assigned_employee_id');
    }

    /**
     * نطاق للطلبات المعلقة
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * نطاق للطلبات قيد المعالجة
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInProgress($query)
    {
        return $query->where('status', self::STATUS_IN_PROGRESS);
    }

    /**
     * نطاق للطلبات المكتملة
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }


}
