<?php

namespace Modules\PromotionManagement\Http\Controllers\Site;

use App\Http\Controllers\Controller;
use Illuminate\View\View;
use Modules\PromotionManagement\Models\Promotion;

/**
 * SitePromotionController
 *
 * Controller مسؤول عن عرض العروض الترويجية في الموقع العام
 * يخدم هذا Controller عرض قائمة العروض النشطة للعملاء
 *
 * بناءً على المهمة: PH03-TASK-038 - BE-CTRL-SITE-PROMOTIONS-LIST-DISPLAY-001
 * يخدم: MOD-PROMO-MGMT-FEAT-001 (عرض بنرات/قائمة العروض الترويجية)
 */
class SitePromotionController extends Controller
{
    /**
     * عرض قائمة العروض الترويجية النشطة في الموقع العام
     *
     * يجلب العروض النشطة التي:
     * - status = true (نشطة)
     * - التاريخ الحالي يقع بين start_date و end_date
     * - مرتبة حسب تاريخ الإنشاء (الأحدث أولاً)
     * - مع تحميل صور البنرات (eager loading)
     * - مع ترقيم 10 عناصر لكل صفحة
     *
     * @return View
     */
    public function index(): View
    {
        // جلب العروض النشطة والجارية مع صور البنرات
        $promotions = Promotion::where('status', true)
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->with('media')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // تمرير البيانات إلى الـ view
        return view('promotionmanagement::site.promotions.index', compact('promotions'));
    }

    /**
     * عرض تفاصيل عرض ترويجي محدد
     *
     * يجلب العرض مع السيارات المرتبطة به وتفاصيلها
     *
     * @param Promotion $promotion العرض المطلوب عرضه
     * @return View
     */
    public function show(Promotion $promotion): View
    {
        // التأكد من أن العرض نشط وجاري
        if (!$promotion->status || $promotion->is_expired || !$promotion->is_started) {
            abort(404, 'العرض غير متاح حالياً');
        }

        // جلب السيارات المرتبطة بالعرض مع تفاصيلها
        $promotion->load([
            'cars' => function ($query) {
                $query->where('is_active', true)
                      ->where('is_sold', false);
            },
            'cars.media',
            'cars.brand',
            'cars.carModel',
            'cars.manufacturingYear'
        ]);

        return view('promotionmanagement::site.promotions.show', compact('promotion'));
    }
}
