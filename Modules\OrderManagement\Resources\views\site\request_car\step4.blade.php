@extends('site.layouts.site_layout')

@section('title', 'اطلب سيارتك - الخطوة الأخيرة: التفاصيل النهائية')

@section('meta_description', 'اطلب سيارتك المثالية من موتور لاين - الخطوة الأخيرة: إدخال التفاصيل النهائية ومعلومات الاتصال')

@section('content')
{{-- Progress Steps --}}
<section class="steps-progress py-4 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="steps-container">
                    <div class="step completed">
                        <div class="step-number">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-title">اختيار الماركة</div>
                    </div>
                    <div class="step-line completed"></div>
                    <div class="step completed">
                        <div class="step-number">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-title">اختيار الموديل</div>
                    </div>
                    <div class="step-line completed"></div>
                    <div class="step completed">
                        <div class="step-number">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-title">اختيار السنة</div>
                    </div>
                    <div class="step-line completed"></div>
                    <div class="step active">
                        <div class="step-number">4</div>
                        <div class="step-title">التفاصيل النهائية</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{{-- Main Content --}}
<section class="request-car-section py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                {{-- Header --}}
                <div class="text-center mb-5">
                    <h1 class="page-title">
                        <i class="fas fa-car me-3 text-primary"></i>
                        اطلب سيارتك
                    </h1>
                    <p class="page-subtitle text-muted">
                        الخطوة الأخيرة: أدخل التفاصيل النهائية ومعلومات الاتصال
                    </p>
                </div>

                {{-- Selected Car Summary --}}
                <div class="selected-summary mb-5">
                    <div class="card border-primary">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-car me-2"></i>
                                ملخص السيارة المطلوبة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 text-center">
                                    <div class="summary-item">
                                        <h6 class="text-primary">الماركة</h6>
                                        <h5>{{ $brand->name }}</h5>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="summary-item">
                                        <h6 class="text-primary">الموديل</h6>
                                        <h5>{{ $model->name }}</h5>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="summary-item">
                                        <h6 class="text-primary">سنة الصنع</h6>
                                        <h5>{{ $year->year }}</h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Request Form --}}
                <div class="request-form">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user me-2"></i>
                                معلومات الاتصال والتفاصيل الإضافية
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="requestCarForm" method="POST">
                                @csrf
                                <div class="row g-4">
                                    {{-- معلومات الاتصال --}}
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-address-card me-2"></i>
                                            معلومات الاتصال
                                        </h6>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="customer_name" class="form-label">
                                            الاسم الكامل <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="customer_name" 
                                               name="customer_name" 
                                               required
                                               placeholder="أدخل اسمك الكامل">
                                        <div class="invalid-feedback"></div>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="customer_phone" class="form-label">
                                            رقم الجوال <span class="text-danger">*</span>
                                        </label>
                                        <input type="tel" 
                                               class="form-control" 
                                               id="customer_phone" 
                                               name="customer_phone" 
                                               required
                                               placeholder="05xxxxxxxx"
                                               pattern="^(05|5)[0-9]{8}$">
                                        <div class="invalid-feedback"></div>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="customer_email" class="form-label">
                                            البريد الإلكتروني
                                        </label>
                                        <input type="email" 
                                               class="form-control" 
                                               id="customer_email" 
                                               name="customer_email" 
                                               placeholder="<EMAIL>">
                                        <div class="invalid-feedback"></div>
                                    </div>

                                    {{-- تفاصيل السيارة المطلوبة --}}
                                    <div class="col-12 mt-4">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-cogs me-2"></i>
                                            تفاصيل السيارة المطلوبة
                                        </h6>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="preferred_color" class="form-label">
                                            اللون المفضل
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="preferred_color" 
                                               name="preferred_color" 
                                               placeholder="مثال: أبيض، أسود، فضي">
                                        <div class="invalid-feedback"></div>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="budget_range" class="form-label">
                                            الميزانية المتوقعة
                                        </label>
                                        <select class="form-select" id="budget_range" name="budget_range">
                                            <option value="">اختر الميزانية</option>
                                            <option value="50000-100000">50,000 - 100,000 ريال</option>
                                            <option value="100000-150000">100,000 - 150,000 ريال</option>
                                            <option value="150000-200000">150,000 - 200,000 ريال</option>
                                            <option value="200000-300000">200,000 - 300,000 ريال</option>
                                            <option value="300000+">أكثر من 300,000 ريال</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>

                                    <div class="col-12">
                                        <label for="additional_features" class="form-label">
                                            الميزات الإضافية المطلوبة
                                        </label>
                                        <textarea class="form-control" 
                                                  id="additional_features" 
                                                  name="additional_features" 
                                                  rows="3"
                                                  placeholder="مثال: فتحة سقف، نظام ملاحة، مقاعد جلدية، كاميرا خلفية..."></textarea>
                                        <div class="invalid-feedback"></div>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="delivery_preference" class="form-label">
                                            تفضيل التسليم
                                        </label>
                                        <select class="form-select" id="delivery_preference" name="delivery_preference">
                                            <option value="">اختر تفضيل التسليم</option>
                                            <option value="showroom">استلام من المعرض</option>
                                            <option value="home_delivery">توصيل للمنزل</option>
                                            <option value="flexible">مرن</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>

                                    <div class="col-12">
                                        <label for="notes" class="form-label">
                                            ملاحظات إضافية
                                        </label>
                                        <textarea class="form-control" 
                                                  id="notes" 
                                                  name="notes" 
                                                  rows="3"
                                                  placeholder="أي ملاحظات أو متطلبات إضافية..."></textarea>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>

                                {{-- Navigation Buttons --}}
                                <div class="navigation-buttons mt-5 text-center">
                                    <a href="{{ route('site.request-car.step3', $model->id) }}" class="btn btn-outline-secondary btn-lg me-3">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        السابق
                                    </a>
                                    <button type="submit" class="btn btn-success btn-lg" id="submitBtn">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        إرسال الطلب
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{{-- Success Modal --}}
<div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0 text-center">
                <div class="w-100">
                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                    <h5 class="modal-title" id="successModalLabel">تم إرسال طلبك بنجاح!</h5>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body text-center">
                <p class="mb-0">سنقوم بالتواصل معك في أقرب وقت ممكن لمناقشة تفاصيل طلبك.</p>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-primary" onclick="window.location.href='{{ route('site.home') }}'">
                    العودة للرئيسية
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* Steps Progress */
.steps-container {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: var(--bs-primary);
    color: white;
}

.step.completed .step-number {
    background: var(--bs-success);
    color: white;
}

.step-title {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

.step.active .step-title,
.step.completed .step-title {
    color: var(--bs-primary);
    font-weight: 600;
}

.step-line {
    flex: 1;
    height: 2px;
    background: #e9ecef;
    margin: 0 1rem;
    position: relative;
    top: -16px;
}

.step-line.completed {
    background: var(--bs-success);
}

/* Summary */
.summary-item {
    padding: 1rem 0;
}

.summary-item h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.summary-item h5 {
    margin-bottom: 0;
    font-weight: 700;
}

/* Form */
.request-form {
    margin-top: 2rem;
}

.navigation-buttons {
    border-top: 1px solid #e9ecef;
    padding-top: 2rem;
}

/* Responsive */
@media (max-width: 768px) {
    .steps-container {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .step-line {
        display: none;
    }
    
    .summary-item {
        padding: 0.5rem 0;
        margin-bottom: 1rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('requestCarForm');
    const submitBtn = document.getElementById('submitBtn');
    const successModal = new bootstrap.Modal(document.getElementById('successModal'));

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // تعطيل زر الإرسال
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
        
        // إزالة رسائل الخطأ السابقة
        clearErrors();
        
        // إرسال البيانات
        const formData = new FormData(form);
        
        fetch('{{ route("site.request-car.submit") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إظهار رسالة النجاح
                successModal.show();
            } else {
                // إظهار رسائل الخطأ
                if (data.errors) {
                    showErrors(data.errors);
                } else {
                    alert(data.message || 'حدث خطأ أثناء إرسال الطلب');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.');
        })
        .finally(() => {
            // إعادة تفعيل زر الإرسال
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>إرسال الطلب';
        });
    });
    
    function clearErrors() {
        const errorElements = form.querySelectorAll('.is-invalid');
        errorElements.forEach(element => {
            element.classList.remove('is-invalid');
        });
        
        const feedbackElements = form.querySelectorAll('.invalid-feedback');
        feedbackElements.forEach(element => {
            element.textContent = '';
        });
    }
    
    function showErrors(errors) {
        for (const [field, messages] of Object.entries(errors)) {
            const input = form.querySelector(`[name="${field}"]`);
            if (input) {
                input.classList.add('is-invalid');
                const feedback = input.nextElementSibling;
                if (feedback && feedback.classList.contains('invalid-feedback')) {
                    feedback.textContent = messages[0];
                }
            }
        }
    }
});
</script>
@endpush
