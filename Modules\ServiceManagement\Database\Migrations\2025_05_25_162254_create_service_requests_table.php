<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration لإنشاء جدول طلبات الخدمات
 *
 * المهمة: PH03-TASK-037 - BE-LOGIC-SITE-SERVICE-REQUEST-SUBMISSION-001
 * بناءً على مواصفات TS-FR.md (DB-TBL-016)
 */
class CreateServiceRequestsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('service_requests', function (Blueprint $table) {
            $table->id(); // DB-COL-SR-001
            $table->unsignedBigInteger('service_id'); // DB-COL-SR-002 - الخدمة المطلوبة
            $table->unsignedBigInteger('user_id')->nullable(); // DB-COL-SR-003 - معرّف العميل (إذا كان مسجلاً)
            $table->string('customer_name', 255); // DB-COL-SR-004 - اسم العميل
            $table->string('customer_phone', 20); // DB-COL-SR-005 - رقم جوال العميل
            $table->string('customer_email', 255)->nullable(); // DB-COL-SR-006 - بريد العميل
            $table->text('notes')->nullable(); // DB-COL-SR-007 - ملاحظات إضافية من العميل
            $table->string('status', 50)->default('new'); // DB-COL-SR-008 - حالة طلب الخدمة
            $table->text('admin_notes')->nullable(); // DB-COL-SR-009 - ملاحظات إدارية
            $table->timestamps(); // DB-COL-SR-010, DB-COL-SR-011

            // المفاتيح الخارجية
            $table->foreign('service_id')
                  ->references('id')
                  ->on('services')
                  ->onDelete('restrict')
                  ->onUpdate('cascade');

            $table->foreign('user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('set null')
                  ->onUpdate('cascade');

            // الفهارس
            $table->index('service_id');
            $table->index('user_id');
            $table->index('customer_name');
            $table->index('customer_phone');
            $table->index('customer_email');
            $table->index('status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('service_requests');
    }
}
