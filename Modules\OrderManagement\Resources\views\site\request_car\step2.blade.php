@extends('site.layouts.site_layout')

@section('title', 'اطلب سيارتك - الخطوة الثانية: اختيار الموديل')

@section('meta_description', 'اطلب سيارتك المثالية من موتور لاين - الخطوة الثانية: اختيار موديل ' . $brand->name)

@section('content')
{{-- Progress Steps --}}
<section class="steps-progress py-4 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="steps-container">
                    <div class="step completed">
                        <div class="step-number">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-title">اختيار الماركة</div>
                    </div>
                    <div class="step-line completed"></div>
                    <div class="step active">
                        <div class="step-number">2</div>
                        <div class="step-title">اختيار الموديل</div>
                    </div>
                    <div class="step-line"></div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-title">اختيار السنة</div>
                    </div>
                    <div class="step-line"></div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-title">التفاصيل النهائية</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{{-- Main Content --}}
<section class="request-car-section py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                {{-- Header --}}
                <div class="text-center mb-5">
                    <h1 class="page-title">
                        <i class="fas fa-car me-3 text-primary"></i>
                        اطلب سيارتك
                    </h1>
                    <p class="page-subtitle text-muted">
                        الخطوة الثانية: اختر موديل {{ $brand->name }}
                    </p>
                </div>

                {{-- Selected Brand Info --}}
                <div class="selected-info mb-4">
                    <div class="card border-primary">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="selected-brand-logo me-3">
                                    @if($brand->hasMedia('brand_logos'))
                                        <img src="{{ $brand->getFirstMediaUrl('brand_logos', 'thumb') }}" 
                                             alt="{{ $brand->name }}" 
                                             class="img-fluid"
                                             style="max-height: 40px;">
                                    @else
                                        <i class="fas fa-car fa-2x text-primary"></i>
                                    @endif
                                </div>
                                <div>
                                    <h6 class="mb-1 text-primary">الماركة المختارة:</h6>
                                    <h5 class="mb-0">{{ $brand->name }}</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Models Grid --}}
                <div class="models-grid">
                    <div class="row g-4">
                        @forelse($models as $model)
                            <div class="col-lg-4 col-md-6">
                                <div class="model-card h-100" data-model-id="{{ $model->id }}">
                                    <div class="model-card-inner">
                                        {{-- Model Icon --}}
                                        <div class="model-icon">
                                            <i class="fas fa-car fa-3x text-primary"></i>
                                        </div>

                                        {{-- Model Name --}}
                                        <div class="model-name">
                                            <h5 class="mb-2">{{ $model->name }}</h5>
                                            <p class="text-muted mb-0">{{ $brand->name }} {{ $model->name }}</p>
                                        </div>

                                        {{-- Selection Indicator --}}
                                        <div class="selection-indicator">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="col-12">
                                <div class="text-center py-5">
                                    <i class="fas fa-car fa-3x text-muted mb-3"></i>
                                    <h4 class="text-muted">لا توجد موديلات متاحة</h4>
                                    <p class="text-muted">لا توجد موديلات متاحة لماركة {{ $brand->name }} حالياً</p>
                                </div>
                            </div>
                        @endforelse
                    </div>
                </div>

                {{-- Navigation Buttons --}}
                <div class="navigation-buttons mt-5 text-center">
                    <a href="{{ route('site.request-car.step1') }}" class="btn btn-outline-secondary btn-lg me-3">
                        <i class="fas fa-arrow-right me-2"></i>
                        السابق
                    </a>
                    <button type="button" id="nextStepBtn" class="btn btn-primary btn-lg" disabled>
                        التالي: اختيار السنة
                        <i class="fas fa-arrow-left ms-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
/* Steps Progress */
.steps-container {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: var(--bs-primary);
    color: white;
}

.step.completed .step-number {
    background: var(--bs-success);
    color: white;
}

.step-title {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

.step.active .step-title,
.step.completed .step-title {
    color: var(--bs-primary);
    font-weight: 600;
}

.step-line {
    flex: 1;
    height: 2px;
    background: #e9ecef;
    margin: 0 1rem;
    position: relative;
    top: -16px;
}

.step-line.completed {
    background: var(--bs-success);
}

/* Selected Info */
.selected-info {
    margin-bottom: 2rem;
}

/* Models Grid */
.models-grid {
    margin-top: 2rem;
}

.model-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
    background: white;
    border: 2px solid #e9ecef;
    position: relative;
}

.model-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    border-color: var(--bs-primary);
}

.model-card.selected {
    border-color: var(--bs-primary);
    background: linear-gradient(135deg, var(--bs-primary) 0%, #0056b3 100%);
    color: white;
}

.model-card-inner {
    padding: 2rem 1.5rem;
    text-align: center;
    position: relative;
}

.model-icon {
    margin-bottom: 1.5rem;
}

.model-card.selected .model-icon i {
    color: white !important;
}

.model-name h5 {
    font-weight: 600;
    transition: color 0.3s ease;
}

.model-card.selected .model-name h5,
.model-card.selected .model-name p {
    color: white;
}

.selection-indicator {
    position: absolute;
    top: 15px;
    left: 15px;
    color: var(--bs-success);
    font-size: 1.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.model-card.selected .selection-indicator {
    opacity: 1;
    color: white;
}

/* Navigation Buttons */
.navigation-buttons {
    border-top: 1px solid #e9ecef;
    padding-top: 2rem;
}

/* Responsive */
@media (max-width: 768px) {
    .steps-container {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .step-line {
        display: none;
    }
    
    .model-card-inner {
        padding: 1.5rem 1rem;
    }
    
    .model-icon {
        margin-bottom: 1rem;
    }
    
    .model-icon i {
        font-size: 2rem !important;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const modelCards = document.querySelectorAll('.model-card');
    const nextStepBtn = document.getElementById('nextStepBtn');
    let selectedModelId = null;

    // Handle model selection
    modelCards.forEach(card => {
        card.addEventListener('click', function() {
            // Remove selection from all cards
            modelCards.forEach(c => c.classList.remove('selected'));
            
            // Add selection to clicked card
            this.classList.add('selected');
            
            // Get selected model ID
            selectedModelId = this.dataset.modelId;
            
            // Enable next button
            nextStepBtn.disabled = false;
            
            // Update next button URL
            nextStepBtn.onclick = function() {
                window.location.href = `{{ route('site.request-car.step3', '') }}/${selectedModelId}`;
            };
        });
    });

    // Handle keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && selectedModelId) {
            window.location.href = `{{ route('site.request-car.step3', '') }}/${selectedModelId}`;
        }
    });
});
</script>
@endpush
