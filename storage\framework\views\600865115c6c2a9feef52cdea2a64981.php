<?php $__env->startSection('title', 'الرئيسية - موتور لاين'); ?>
<?php $__env->startSection('meta_description', 'موتور لاين - وجهتك الأولى لشراء السيارات الجديدة بأفضل الأسعار وأعلى جودة خدمة في المملكة العربية السعودية'); ?>

<?php $__env->startSection('content'); ?>
<div class="homepage-container">
    
    <?php if($banners && $banners->count() > 0): ?>
    <section class="hero-banners mb-5">
        <div id="mainCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="5000">
            
            <div class="carousel-indicators">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <button type="button" data-bs-target="#mainCarousel" data-bs-slide-to="<?php echo e($index); ?>"
                        class="<?php echo e($index === 0 ? 'active' : ''); ?>" aria-current="<?php echo e($index === 0 ? 'true' : 'false'); ?>"
                        aria-label="السلايد <?php echo e($index + 1); ?>"></button>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            
            <div class="carousel-inner">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="carousel-item <?php echo e($index === 0 ? 'active' : ''); ?>">
                    <img src="<?php echo e($banner->getFirstMediaUrl('homepage_banners') ?: asset('images/placeholder-banner.jpg')); ?>"
                         class="d-block w-100 hero-image" alt="<?php echo e($banner->title ?? 'Banner'); ?>">
                    <div class="carousel-caption d-md-block">
                        <div class="hero-content">
                            <?php if($banner->title): ?>
                            <h1 class="hero-title"><?php echo e($banner->title); ?></h1>
                            <?php endif; ?>
                            <?php if($banner->subtitle): ?>
                            <p class="hero-subtitle"><?php echo e($banner->subtitle); ?></p>
                            <?php endif; ?>
                            <?php if($banner->button_link): ?>
                            <a href="<?php echo e($banner->button_link); ?>" class="btn btn-primary btn-lg hero-btn">
                                <?php echo e($banner->button_text ?? 'اكتشف المزيد'); ?>

                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            
            <button class="carousel-control-prev" type="button" data-bs-target="#mainCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">السابق</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#mainCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">التالي</span>
            </button>
        </div>
    </section>
    <?php else: ?>
    
    <section class="hero-section bg-gradient-primary text-white py-5 mb-5">
        <div class="container">
            <div class="row align-items-center min-vh-50">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">مرحباً بك في موتور لاين</h1>
                    <p class="lead mb-4">وجهتك الأولى لشراء السيارات الجديدة بأفضل الأسعار وأعلى جودة خدمة في المملكة العربية السعودية</p>
                    <div class="hero-actions">
                        <a href="<?php echo e(route('site.cars.index')); ?>" class="btn btn-light btn-lg me-3">
                            <i class="fas fa-car me-2"></i>تصفح السيارات
                        </a>
                        <a href="#" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-phone me-2"></i>اتصل بنا
                        </a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <img src="<?php echo e(asset('images/hero-car.png')); ?>" alt="سيارة" class="img-fluid hero-car-image">
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    
    <section class="quick-search-section py-4 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="search-card bg-white p-4 rounded shadow-sm">
                        <h3 class="text-center mb-4">ابحث عن سيارتك المثالية</h3>
                        <form action="<?php echo e(route('site.cars.index')); ?>" method="GET" class="quick-search-form">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <select name="brand" class="form-select">
                                        <option value="">اختر الماركة</option>
                                        
                                        <option value="toyota">تويوتا</option>
                                        <option value="honda">هوندا</option>
                                        <option value="nissan">نيسان</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select name="model" class="form-select">
                                        <option value="">اختر الموديل</option>
                                        
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select name="year" class="form-select">
                                        <option value="">السنة</option>
                                        <?php for($year = date('Y') + 1; $year >= 2015; $year--): ?>
                                        <option value="<?php echo e($year); ?>"><?php echo e($year); ?></option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select name="price_range" class="form-select">
                                        <option value="">نطاق السعر</option>
                                        <option value="0-50000">أقل من 50,000</option>
                                        <option value="50000-100000">50,000 - 100,000</option>
                                        <option value="100000-200000">100,000 - 200,000</option>
                                        <option value="200000+">أكثر من 200,000</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-search me-2"></i>بحث
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    
    <?php if($featuredCars && $featuredCars->count() > 0): ?>
    <section class="featured-cars py-5">
        <div class="container">
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h2 class="section-title">أحدث ما وصلنا</h2>
                    <p class="section-subtitle text-muted">اكتشف مجموعتنا المختارة من أفضل السيارات الجديدة</p>
                </div>
            </div>
            <div class="row">
                <?php $__currentLoopData = $featuredCars; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $car): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="car-card h-100 shadow-sm">
                        <div class="car-image position-relative">
                            <img src="<?php echo e($car->getFirstMediaUrl('car_images', 'thumbnail') ?: asset('images/placeholder-car.jpg')); ?>"
                                 class="card-img-top" alt="<?php echo e($car->brand->name ?? ''); ?> <?php echo e($car->carModel->name ?? ''); ?>">
                            <?php if($car->is_featured): ?>
                            <div class="car-badge">
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-star me-1"></i>مميزة
                                </span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if($car->offer_price && $car->offer_price < $car->total_price): ?>
                            <div class="offer-badge">
                                <span class="badge bg-danger">
                                    <i class="fas fa-tag me-1"></i>عرض خاص
                                </span>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="card-body d-flex flex-column">
                            
                            <h5 class="card-title mb-2">
                                <?php echo e($car->brand->name ?? ''); ?> <?php echo e($car->carModel->name ?? ''); ?>

                                <?php if($car->trim_name): ?> <?php echo e($car->trim_name); ?> <?php endif; ?>
                                <?php echo e($car->manufacturingYear->year ?? ''); ?>

                            </h5>

                            
                            <div class="car-price mb-3">
                                <?php if($car->offer_price && $car->offer_price < $car->total_price): ?>
                                    <span class="price-original text-decoration-line-through text-muted">
                                        <?php echo e(number_format($car->total_price, 0)); ?> ر.س
                                    </span>
                                    <span class="price-offer text-success fw-bold d-block">
                                        <?php echo e(number_format($car->offer_price, 0)); ?> ر.س
                                    </span>
                                <?php else: ?>
                                    <span class="price-amount fw-bold text-primary">
                                        <?php echo e(number_format($car->total_price, 2)); ?> ر.س
                                    </span>
                                <?php endif; ?>
                            </div>

                            
                            <div class="car-specs mb-3 text-muted small">
                                <div class="row g-2">
                                    <?php if($car->transmission_type): ?>
                                    <div class="col-6">
                                        <i class="fas fa-cogs me-1"></i><?php echo e($car->transmission_type); ?>

                                    </div>
                                    <?php endif; ?>
                                    <?php if($car->fuel_type): ?>
                                    <div class="col-6">
                                        <i class="fas fa-gas-pump me-1"></i><?php echo e($car->fuel_type); ?>

                                    </div>
                                    <?php endif; ?>
                                    <?php if($car->engine_capacity): ?>
                                    <div class="col-6">
                                        <i class="fas fa-tachometer-alt me-1"></i><?php echo e($car->engine_capacity); ?>

                                    </div>
                                    <?php endif; ?>
                                    <?php if($car->body_type): ?>
                                    <div class="col-6">
                                        <i class="fas fa-car me-1"></i><?php echo e($car->body_type); ?>

                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            
                            <div class="car-actions mt-auto">
                                <div class="row g-2">
                                    <div class="col-8">
                                        <a href="<?php echo e(route('site.cars.show', $car->id)); ?>" class="btn btn-primary w-100">
                                            <i class="fas fa-eye me-1"></i>عرض التفاصيل
                                        </a>
                                    </div>
                                    <div class="col-4">
                                        <button class="btn btn-outline-secondary w-100" onclick="toggleFavorite(<?php echo e($car->id); ?>)"
                                                title="إضافة للمفضلة">
                                            <i class="far fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <div class="text-center mt-4">
                <a href="<?php echo e(route('site.cars.index')); ?>" class="btn btn-outline-primary btn-lg">
                    عرض جميع السيارات <i class="fas fa-arrow-left ms-2"></i>
                </a>
            </div>
        </div>
    </section>
    <?php endif; ?>

    
    <?php if($latestPromotions && $latestPromotions->count() > 0): ?>
    <section class="latest-promotions py-5 bg-light">
        <div class="container">
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h2 class="section-title">لا تفوت عروضنا الحصرية</h2>
                    <p class="section-subtitle text-muted">اكتشف أفضل العروض والخصومات المحدودة</p>
                </div>
            </div>
            <div class="row">
                <?php $__currentLoopData = $latestPromotions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $promotion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="promotion-card h-100 shadow-sm">
                        <div class="promotion-image position-relative">
                            <img src="<?php echo e($promotion->getFirstMediaUrl('promotion_banners') ?: asset('images/placeholder-promotion.jpg')); ?>"
                                 class="card-img-top" alt="<?php echo e($promotion->name); ?>">
                            <div class="promotion-overlay">
                                <span class="badge bg-danger position-absolute top-0 start-0 m-2">
                                    <i class="fas fa-fire me-1"></i>عرض محدود
                                </span>
                            </div>
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title"><?php echo e($promotion->name); ?></h5>
                            <?php if($promotion->description): ?>
                            <p class="card-text text-muted flex-grow-1"><?php echo e(Str::limit($promotion->description, 80)); ?></p>
                            <?php endif; ?>
                            <div class="promotion-actions mt-auto">
                                <a href="<?php echo e(route('site.promotions.show', $promotion->id)); ?>" class="btn btn-success w-100">
                                    <i class="fas fa-tag me-1"></i>اكتشف العرض
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <div class="text-center mt-4">
                <a href="#" class="btn btn-outline-success btn-lg">
                    عرض جميع العروض <i class="fas fa-arrow-left ms-2"></i>
                </a>
            </div>
        </div>
    </section>
    <?php endif; ?>

    
    <section class="services-section py-5">
        <div class="container">
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h2 class="section-title">خدمات متكاملة تلبي احتياجاتك</h2>
                    <p class="section-subtitle text-muted">نقدم لك مجموعة شاملة من الخدمات المتخصصة</p>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card text-center h-100 p-4 bg-white rounded shadow-sm">
                        <div class="service-icon mb-3">
                            <i class="fas fa-car fa-3x text-primary"></i>
                        </div>
                        <h5 class="service-title mb-3">بيع السيارات الجديدة</h5>
                        <p class="text-muted">تشكيلة واسعة من أحدث السيارات من أفضل الماركات العالمية بأسعار تنافسية</p>
                        <a href="<?php echo e(route('site.cars.index')); ?>" class="btn btn-outline-primary btn-sm mt-2">
                            تصفح السيارات
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card text-center h-100 p-4 bg-white rounded shadow-sm">
                        <div class="service-icon mb-3">
                            <i class="fas fa-tools fa-3x text-primary"></i>
                        </div>
                        <h5 class="service-title mb-3">الصيانة والضمان</h5>
                        <p class="text-muted">خدمات صيانة شاملة وضمان ممتد على أيدي فنيين معتمدين</p>
                        <a href="#" class="btn btn-outline-primary btn-sm mt-2">
                            اكتشف الخدمات
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card text-center h-100 p-4 bg-white rounded shadow-sm">
                        <div class="service-icon mb-3">
                            <i class="fas fa-handshake fa-3x text-primary"></i>
                        </div>
                        <h5 class="service-title mb-3">حلول التمويل</h5>
                        <p class="text-muted">حلول تمويل مرنة ومناسبة مع أفضل البنوك وشركات التمويل</p>
                        <a href="#" class="btn btn-outline-primary btn-sm mt-2">
                            تعرف على التمويل
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card text-center h-100 p-4 bg-white rounded shadow-sm">
                        <div class="service-icon mb-3">
                            <i class="fas fa-building fa-3x text-primary"></i>
                        </div>
                        <h5 class="service-title mb-3">مبيعات الشركات</h5>
                        <p class="text-muted">حلول متخصصة للشركات والأساطيل بأسعار وخدمات مميزة</p>
                        <a href="#" class="btn btn-outline-primary btn-sm mt-2">
                            طلب عرض سعر
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card text-center h-100 p-4 bg-white rounded shadow-sm">
                        <div class="service-icon mb-3">
                            <i class="fas fa-shipping-fast fa-3x text-primary"></i>
                        </div>
                        <h5 class="service-title mb-3">التوصيل المجاني</h5>
                        <p class="text-muted">خدمة توصيل مجانية لسيارتك الجديدة إلى باب منزلك</p>
                        <a href="#" class="btn btn-outline-primary btn-sm mt-2">
                            تفاصيل التوصيل
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card text-center h-100 p-4 bg-white rounded shadow-sm">
                        <div class="service-icon mb-3">
                            <i class="fas fa-headset fa-3x text-primary"></i>
                        </div>
                        <h5 class="service-title mb-3">دعم العملاء</h5>
                        <p class="text-muted">فريق دعم متخصص متاح على مدار الساعة لخدمتك</p>
                        <a href="#" class="btn btn-outline-primary btn-sm mt-2">
                            تواصل معنا
                        </a>
                    </div>
                </div>
            </div>
            <div class="text-center mt-4">
                <a href="#" class="btn btn-primary btn-lg">
                    اكتشف جميع خدماتنا <i class="fas fa-arrow-left ms-2"></i>
                </a>
            </div>
        </div>
    </section>

    
    <section class="why-choose-us py-5 bg-light">
        <div class="container">
            <div class="row mb-5">
                <div class="col-12 text-center">
                    <h2 class="section-title">لماذا تختارنا؟</h2>
                    <p class="section-subtitle text-muted">نقاط القوة التي تجعلنا الخيار الأفضل لك</p>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-item text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-award fa-3x text-warning"></i>
                        </div>
                        <h5 class="feature-title">تشكيلة واسعة</h5>
                        <p class="text-muted">أكبر تشكيلة من السيارات الجديدة من جميع الماركات العالمية</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-item text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-dollar-sign fa-3x text-success"></i>
                        </div>
                        <h5 class="feature-title">أسعار تنافسية</h5>
                        <p class="text-muted">أفضل الأسعار في السوق مع عروض وخصومات حصرية</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-item text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-users fa-3x text-info"></i>
                        </div>
                        <h5 class="feature-title">خدمة عملاء ممتازة</h5>
                        <p class="text-muted">فريق متخصص يقدم لك أفضل تجربة شراء ومتابعة مستمرة</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-item text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-mobile-alt fa-3x text-primary"></i>
                        </div>
                        <h5 class="feature-title">سهولة الشراء أونلاين</h5>
                        <p class="text-muted">اشتر سيارتك بكل سهولة من منزلك عبر موقعنا أو تطبيقنا</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    
    <section class="custom-car-request py-5 bg-primary text-white">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h2 class="mb-3">لم تجد السيارة التي تبحث عنها؟</h2>
                    <p class="lead mb-4">اطلب سيارتك المخصصة وسنوفرها لك بأفضل الأسعار والمواصفات التي تريدها</p>
                    <ul class="list-unstyled mb-4">
                        <li class="mb-2"><i class="fas fa-check me-2"></i>اختر الماركة والموديل والمواصفات</li>
                        <li class="mb-2"><i class="fas fa-check me-2"></i>احصل على عرض سعر مخصص</li>
                        <li class="mb-2"><i class="fas fa-check me-2"></i>متابعة حالة طلبك خطوة بخطوة</li>
                    </ul>
                </div>
                <div class="col-lg-4 text-center">
                    <a href="#" class="btn btn-light btn-lg px-5 py-3">
                        <i class="fas fa-plus-circle me-2"></i>اطلب سيارتك الآن
                    </a>
                </div>
            </div>
        </div>
    </section>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* أنماط السلايدر الرئيسي */
.hero-image {
    height: 500px;
    object-fit: cover;
}

.hero-content {
    background: rgba(0, 0, 0, 0.5);
    padding: 2rem;
    border-radius: 10px;
    backdrop-filter: blur(5px);
}

.hero-title {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.hero-btn {
    padding: 12px 30px;
    font-size: 1.1rem;
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.min-vh-50 {
    min-height: 50vh;
}

.hero-car-image {
    filter: drop-shadow(0 10px 20px rgba(0,0,0,0.2));
}

/* أنماط البحث السريع */
.search-card {
    border: none;
    border-radius: 15px;
}

.quick-search-form .form-select,
.quick-search-form .btn {
    border-radius: 8px;
    padding: 12px 15px;
}

/* أنماط بطاقات السيارات */
.car-card {
    border: 1px solid #e0e0e0;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    background: white;
}

.car-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    border-color: var(--primary-color);
}

.car-image {
    overflow: hidden;
    height: 200px;
}

.car-image img {
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.car-card:hover .car-image img {
    transform: scale(1.05);
}

.car-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
}

.offer-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 2;
}

.car-specs {
    font-size: 0.9rem;
}

.price-original {
    font-size: 0.9rem;
}

.price-offer {
    font-size: 1.3rem;
}

.price-amount {
    font-size: 1.25rem;
}

/* أنماط بطاقات العروض */
.promotion-card {
    border: 1px solid #e0e0e0;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    background: white;
}

.promotion-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.promotion-image {
    height: 200px;
    overflow: hidden;
}

.promotion-image img {
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.promotion-card:hover .promotion-image img {
    transform: scale(1.05);
}

/* أنماط بطاقات الخدمات */
.service-card {
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.service-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.service-icon i {
    transition: color 0.3s ease;
}

.service-card:hover .service-icon i {
    color: var(--secondary-color) !important;
}

/* أنماط قسم "لماذا تختارنا" */
.feature-item {
    padding: 2rem 1rem;
}

.feature-icon i {
    transition: transform 0.3s ease;
}

.feature-item:hover .feature-icon i {
    transform: scale(1.1);
}

/* أنماط العناوين والنصوص */
.section-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: var(--text-color);
}

.section-subtitle {
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

/* أنماط الأزرار */
.btn {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .car-image {
        height: 180px;
    }

    .promotion-image {
        height: 150px;
    }
}

@media (max-width: 576px) {
    .hero-content {
        padding: 1rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .section-title {
        font-size: 1.75rem;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة السلايدر الرئيسي
    const mainCarousel = document.getElementById('mainCarousel');
    if (mainCarousel) {
        // إضافة تأثيرات إضافية للسلايدر
        mainCarousel.addEventListener('slide.bs.carousel', function (event) {
            // يمكن إضافة تأثيرات مخصصة هنا
        });
    }

    // تهيئة نموذج البحث السريع
    const brandSelect = document.querySelector('select[name="brand"]');
    const modelSelect = document.querySelector('select[name="model"]');

    if (brandSelect && modelSelect) {
        brandSelect.addEventListener('change', function() {
            const selectedBrand = this.value;

            // مسح خيارات الموديل الحالية
            modelSelect.innerHTML = '<option value="">اختر الموديل</option>';

            if (selectedBrand) {
                // هنا سيتم جلب الموديلات بناءً على الماركة المختارة
                // سيتم تنفيذ هذا عبر AJAX لاحقاً
                console.log('Selected brand:', selectedBrand);

                // مثال على إضافة خيارات مؤقتة
                const sampleModels = {
                    'toyota': ['كامري', 'كورولا', 'لاند كروزر', 'برادو'],
                    'honda': ['أكورد', 'سيفيك', 'سي آر في', 'بايلوت'],
                    'nissan': ['التيما', 'سنترا', 'باترول', 'إكس تريل']
                };

                if (sampleModels[selectedBrand]) {
                    sampleModels[selectedBrand].forEach(function(model) {
                        const option = document.createElement('option');
                        option.value = model.toLowerCase().replace(/\s+/g, '-');
                        option.textContent = model;
                        modelSelect.appendChild(option);
                    });
                }
            }
        });
    }

    // تحسين تأثيرات التحويم للبطاقات
    const carCards = document.querySelectorAll('.car-card');
    carCards.forEach(function(card) {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // تحسين تأثيرات بطاقات الخدمات
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(function(card) {
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.service-icon i');
            if (icon) {
                icon.style.transform = 'scale(1.1)';
            }
        });

        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.service-icon i');
            if (icon) {
                icon.style.transform = 'scale(1)';
            }
        });
    });

    // تحسين الأداء للصور
    const images = document.querySelectorAll('img[data-src]');
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(function(img) {
            imageObserver.observe(img);
        });
    }
});

// وظيفة إضافة/إزالة من المفضلة
function toggleFavorite(carId) {
    const button = event.target.closest('button');
    const icon = button.querySelector('i');

    // تغيير الأيقونة مؤقتاً
    if (icon.classList.contains('far')) {
        icon.classList.remove('far');
        icon.classList.add('fas');
        icon.style.color = '#dc3545';
        button.title = 'إزالة من المفضلة';

        // إظهار رسالة نجاح
        showToast('تم إضافة السيارة للمفضلة', 'success');
    } else {
        icon.classList.remove('fas');
        icon.classList.add('far');
        icon.style.color = '';
        button.title = 'إضافة للمفضلة';

        // إظهار رسالة نجاح
        showToast('تم إزالة السيارة من المفضلة', 'info');
    }

    // هنا سيتم إرسال طلب AJAX لحفظ التغيير في قاعدة البيانات
    console.log('Toggle favorite for car:', carId);

    // TODO: تنفيذ AJAX request
    /*
    fetch('/api/cars/' + carId + '/toggle-favorite', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تم الحفظ بنجاح
        } else {
            // إعادة الأيقونة لحالتها السابقة في حالة الخطأ
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // إعادة الأيقونة لحالتها السابقة في حالة الخطأ
    });
    */
}

// وظيفة إظهار رسائل التنبيه
function showToast(message, type = 'info') {
    // إنشاء عنصر التنبيه
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
    `;

    // إضافة التنبيه للصفحة
    document.body.appendChild(toast);

    // إزالة التنبيه تلقائياً بعد 3 ثوانٍ
    setTimeout(function() {
        if (toast.parentNode) {
            const bsAlert = new bootstrap.Alert(toast);
            bsAlert.close();
        }
    }, 3000);
}

// تحسين الأداء للتمرير السلس
function smoothScrollTo(target) {
    const element = document.querySelector(target);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('site.layouts.site_layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Project\MotorLine_10\resources\views/site/home.blade.php ENDPATH**/ ?>