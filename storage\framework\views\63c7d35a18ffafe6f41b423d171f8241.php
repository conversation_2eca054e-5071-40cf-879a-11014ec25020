<?php $__env->startSection('title', 'خدماتنا - موتور لاين'); ?>

<?php $__env->startSection('meta_description', 'تصفح قائمة الخدمات الإضافية التي يقدمها معرض موتور لاين للسيارات'); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-5">
    
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="brand-heading-primary mb-3">
                <i class="fas fa-tools me-3"></i>
                خدماتنا المتميزة
            </h1>
            <p class="lead text-muted">
                نقدم لك مجموعة شاملة من الخدمات الإضافية لضمان أفضل تجربة مع سيارتك
            </p>
        </div>
    </div>

    
    <?php if(isset($error)): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert brand-alert brand-alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo e($error); ?>

                </div>
            </div>
        </div>
    <?php endif; ?>

    
    <?php if($categories->count() > 0): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card brand-card">
                    <div class="card-body">
                        <h5 class="card-title mb-3">
                            <i class="fas fa-filter me-2"></i>
                            تصفية حسب الفئة
                        </h5>
                        <div class="d-flex flex-wrap gap-2">
                            <button class="btn brand-btn-outline-primary btn-sm filter-btn active" data-category="all">
                                جميع الخدمات
                                <span class="badge bg-primary ms-1"><?php echo e($services->count()); ?></span>
                            </button>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <button class="btn brand-btn-outline-primary btn-sm filter-btn" data-category="<?php echo e($category->id); ?>">
                                    <?php echo e($category->name); ?>

                                    <span class="badge bg-primary ms-1"><?php echo e($category->services_count); ?></span>
                                </button>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    
    <?php if($services->count() > 0): ?>
        <div class="row" id="services-container">
            <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-4 col-md-6 mb-4 service-card" data-category="<?php echo e($service->category_id ?? 'uncategorized'); ?>">
                    <div class="card brand-card h-100 shadow-sm">
                        
                        <?php if($service->main_image_url): ?>
                            <img src="<?php echo e($service->main_image_url); ?>"
                                 class="card-img-top"
                                 alt="<?php echo e($service->name); ?>"
                                 style="height: 200px; object-fit: cover;">
                        <?php else: ?>
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                                 style="height: 200px;">
                                <i class="fas fa-tools fa-3x text-muted"></i>
                            </div>
                        <?php endif; ?>

                        <div class="card-body d-flex flex-column">
                            
                            <?php if($service->category): ?>
                                <div class="mb-2">
                                    <span class="badge brand-badge-secondary">
                                        <?php echo e($service->category->name); ?>

                                    </span>
                                </div>
                            <?php endif; ?>

                            
                            <h5 class="card-title brand-text-primary">
                                <?php echo e($service->name); ?>

                            </h5>

                            
                            <?php if($service->description): ?>
                                <p class="card-text text-muted flex-grow-1">
                                    <?php echo e(Str::limit($service->description, 120)); ?>

                                </p>
                            <?php endif; ?>

                            
                            <div class="mb-3">
                                <span class="h5 brand-text-primary fw-bold">
                                    <?php echo e($service->formatted_price); ?>

                                </span>
                            </div>

                            
                            <div class="mt-auto">
                                <div class="d-grid gap-2">
                                    <button type="button"
                                            class="btn brand-btn-primary request-service-btn"
                                            data-service-id="<?php echo e($service->id); ?>"
                                            data-service-name="<?php echo e($service->name); ?>"
                                            data-bs-toggle="modal"
                                            data-bs-target="#serviceRequestModal">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        اطلب الخدمة
                                    </button>
                                    <a href="<?php echo e(route('site.services.show', $service->id)); ?>"
                                       class="btn brand-btn-outline-primary">
                                        <i class="fas fa-info-circle me-2"></i>
                                        اعرف المزيد
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        
        <div class="row d-none" id="no-results">
            <div class="col-12 text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد خدمات في هذه الفئة</h4>
                <p class="text-muted">جرب اختيار فئة أخرى أو تصفح جميع الخدمات</p>
            </div>
        </div>
    <?php else: ?>
        
        <div class="row">
            <div class="col-12 text-center py-5">
                <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد خدمات متاحة حالياً</h4>
                <p class="text-muted">نعمل على إضافة المزيد من الخدمات قريباً</p>
                <a href="<?php echo e(route('site.home')); ?>" class="btn brand-btn-primary mt-3">
                    <i class="fas fa-home me-2"></i>
                    العودة للرئيسية
                </a>
            </div>
        </div>
    <?php endif; ?>
</div>


<?php echo $__env->make('servicemanagement::site.services._request_form', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // فلترة الخدمات حسب الفئة
    const filterButtons = document.querySelectorAll('.filter-btn');
    const serviceCards = document.querySelectorAll('.service-card');
    const noResults = document.getElementById('no-results');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.dataset.category;

            // تحديث حالة الأزرار
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            let visibleCount = 0;

            // إظهار/إخفاء البطاقات
            serviceCards.forEach(card => {
                if (category === 'all' || card.dataset.category === category) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            // إظهار رسالة عدم وجود نتائج
            if (visibleCount === 0) {
                noResults.classList.remove('d-none');
            } else {
                noResults.classList.add('d-none');
            }
        });
    });

    // ملاحظة: تم نقل منطق معالجة نموذج طلب الخدمة إلى المكون _request_form.blade.php
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.service-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.filter-btn {
    transition: all 0.3s ease;
}

.filter-btn.active {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
}

.card-img-top {
    transition: transform 0.3s ease;
}

.service-card:hover .card-img-top {
    transform: scale(1.05);
}

.badge {
    font-size: 0.75em;
}

@media (max-width: 768px) {
    .filter-btn {
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('site.layouts.site_layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Project\MotorLine_10\Modules/ServiceManagement\Resources/views/site/services/index.blade.php ENDPATH**/ ?>