<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    {{-- العنوان الديناميكي --}}
    <title>@yield('title', 'موتور لاين - معرض السيارات الجديدة')</title>

    {{-- Meta Tags للـ SEO --}}
    @php
        $metaDescription = trim($__env->yieldContent('meta_description')) ?: 'موتور لاين - وجهتك الأولى لشراء السيارات الجديدة بأفضل الأسعار وأعلى جودة خدمة في المملكة العربية السعودية';
        $metaKeywords = trim($__env->yieldContent('meta_keywords')) ?: 'سيارات جديدة، معرض سيارات، شراء سيارة، تمويل سيارات، السعودية، الرياض';
        $ogTitle = trim($__env->yieldContent('og_title')) ?: (trim($__env->yieldContent('title')) ?: 'موتور لاين - معرض السيارات الجديدة');
        $ogDescription = trim($__env->yieldContent('og_description')) ?: $metaDescription;
        $ogImage = trim($__env->yieldContent('og_image')) ?: asset('images/logo-og.jpg');
        $ogUrl = trim($__env->yieldContent('og_url')) ?: url()->current();
        $ogType = trim($__env->yieldContent('og_type')) ?: 'website';
    @endphp

    <meta name="description" content="{{ $metaDescription }}">
    <meta name="keywords" content="{{ $metaKeywords }}">
    <meta name="author" content="موتور لاين">

    {{-- Open Graph Meta Tags --}}
    <meta property="og:title" content="{{ $ogTitle }}">
    <meta property="og:description" content="{{ $ogDescription }}">
    <meta property="og:image" content="{{ $ogImage }}">
    <meta property="og:url" content="{{ $ogUrl }}">
    <meta property="og:type" content="{{ $ogType }}">
    <meta property="og:site_name" content="موتور لاين">
    <meta property="og:locale" content="ar_SA">

    {{-- Twitter Card Meta Tags --}}
    @php
        $twitterCard = trim($__env->yieldContent('twitter_card')) ?: 'summary_large_image';
        $twitterTitle = trim($__env->yieldContent('twitter_title')) ?: $ogTitle;
        $twitterDescription = trim($__env->yieldContent('twitter_description')) ?: $ogDescription;
        $twitterImage = trim($__env->yieldContent('twitter_image')) ?: $ogImage;
    @endphp

    <meta name="twitter:card" content="{{ $twitterCard }}">
    <meta name="twitter:title" content="{{ $twitterTitle }}">
    <meta name="twitter:description" content="{{ $twitterDescription }}">
    <meta name="twitter:image" content="{{ $twitterImage }}">

    {{-- Additional Meta Tags --}}
    @stack('meta_tags')



    {{-- Favicon --}}
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('images/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('images/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('images/favicon-16x16.png') }}">

    {{-- Fonts --}}
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    {{-- Font Awesome --}}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
          crossorigin="anonymous" referrerpolicy="no-referrer">

    {{-- Bootstrap RTL --}}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet"
          integrity="sha384-PJsj/BTMqILvmcej7ulplguok8ag4xFTPryRq8xevL7eBYSmpXKcbNVuy+P0RMgq"
          crossorigin="anonymous">

    {{-- Site App CSS (يحتوي على متغيرات CSS مخصصة) --}}
    @vite(['resources/css/site_app.css'])

    {{-- Brand Identity CSS (يجب تحميله بعد متغيرات CSS المخصصة) --}}
    <link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">

    {{-- Additional CSS --}}
    @stack('styles')

    {{-- Custom CSS for this layout --}}
    <style>
        /* إعدادات عامة للموقع */
        :root {
            --font-family-arabic: 'IBM Plex Sans Arabic', sans-serif;
        }

        * {
            font-family: var(--font-family-arabic);
        }

        body {
            background-color: var(--light-bg);
            color: var(--text-color);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            flex: 1;
            padding-top: 2rem;
            padding-bottom: 2rem;
        }

        /* تحسينات للـ RTL */
        .rtl-fix {
            direction: rtl;
            text-align: right;
        }

        /* تحسينات الأداء */
        img {
            max-width: 100%;
            height: auto;
        }

        /* تحسينات الوصولية */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* تحسينات للطباعة */
        @media print {
            .site-header,
            .site-footer,
            .btn,
            .navbar {
                display: none !important;
            }

            .main-content {
                padding: 0;
            }
        }

        /* Loading States */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--secondary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Scroll to Top Button */
        .scroll-to-top {
            position: fixed;
            bottom: 30px;
            left: 30px;
            width: 50px;
            height: 50px;
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 50%;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all var(--transition-speed) ease;
            z-index: 1000;
            box-shadow: var(--box-shadow);
        }

        .scroll-to-top:hover {
            background-color: var(--accent-color);
            transform: translateY(-3px);
        }

        .scroll-to-top.show {
            display: flex;
        }
    </style>
</head>

<body>
    {{-- Loading Overlay (يمكن إظهاره/إخفاؤه بـ JavaScript) --}}
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
    </div>

    {{-- الرأس --}}
    @include('site.layouts.partials._header')

    {{-- المحتوى الرئيسي --}}
    <main class="main-content">
        {{-- رسائل التنبيه --}}
        @if(session('success'))
            <div class="container">
                <div class="alert brand-alert brand-alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                </div>
            </div>
        @endif

        @if(session('error'))
            <div class="container">
                <div class="alert brand-alert brand-alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                </div>
            </div>
        @endif

        @if(session('warning'))
            <div class="container">
                <div class="alert brand-alert brand-alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ session('warning') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                </div>
            </div>
        @endif

        @if(session('info'))
            <div class="container">
                <div class="alert brand-alert brand-alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    {{ session('info') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                </div>
            </div>
        @endif

        {{-- محتوى الصفحة --}}
        @yield('content')
    </main>

    {{-- التذييل --}}
    @include('site.layouts.partials._footer')

    {{-- زر العودة للأعلى --}}
    <button class="scroll-to-top" id="scrollToTop" title="العودة للأعلى">
        <i class="fas fa-chevron-up"></i>
    </button>

    {{-- Bootstrap JavaScript --}}
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz"
            crossorigin="anonymous"></script>

    {{-- Site App JavaScript (سيتم إنشاؤه لاحقاً) --}}
    @vite(['resources/js/site_app.js'])

    {{-- Additional JavaScript --}}
    @stack('scripts')

    {{-- Common JavaScript --}}
    <script>
        // إعدادات عامة
        document.addEventListener('DOMContentLoaded', function() {
            // زر العودة للأعلى
            const scrollToTopBtn = document.getElementById('scrollToTop');

            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    scrollToTopBtn.classList.add('show');
                } else {
                    scrollToTopBtn.classList.remove('show');
                }
            });

            scrollToTopBtn.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            // إخفاء التنبيهات تلقائياً بعد 5 ثوانٍ
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });

            // تحسين تجربة المستخدم للنماذج
            const forms = document.querySelectorAll('form');
            forms.forEach(function(form) {
                form.addEventListener('submit', function() {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
                    }
                });
            });
        });

        // دوال مساعدة عامة
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        function showToast(message, type = 'success') {
            // يمكن تطوير نظام Toast مخصص هنا
            console.log(`${type}: ${message}`);
        }
    </script>
</body>
</html>
