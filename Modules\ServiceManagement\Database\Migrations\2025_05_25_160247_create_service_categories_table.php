<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateServiceCategoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('service_categories', function (Blueprint $table) {
            $table->id();
            $table->json('name'); // للترجمة باستخدام spatie/laravel-translatable
            $table->json('description')->nullable(); // للترجمة باستخدام spatie/laravel-translatable
            $table->boolean('status')->default(true); // حالة الفئة (نشطة/غير نشطة)
            $table->integer('display_order')->default(0); // ترتيب العرض
            $table->timestamps();
            $table->softDeletes();

            // الفهارس
            $table->index('status');
            $table->index('display_order');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('service_categories');
    }
}
