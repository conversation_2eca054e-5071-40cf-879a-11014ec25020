<?php

namespace Modules\ServiceManagement\Http\Controllers\Site;

use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Modules\ServiceManagement\Models\Service;
use Modules\ServiceManagement\Models\ServiceCategory;
use Modules\ServiceManagement\Models\ServiceRequest;
use Modules\ServiceManagement\Http\Requests\Site\ServiceRequestRequest;
use Modules\ServiceManagement\Notifications\NewServiceRequestNotification;
use Modules\UserManagement\Models\User;

/**
 * Site Service Controller
 *
 * يدير عرض الخدمات في الموقع العام
 * بناءً على PH03-TASK-034 و MOD-SERVICE-MGMT-FEAT-001
 *
 * @package Modules\ServiceManagement\Http\Controllers\Site
 * <AUTHOR> System
 * @version 1.0.0
 */
class SiteServiceController extends Controller
{
    /**
     * عرض قائمة الخدمات المتاحة في الموقع العام
     *
     * يجلب الخدمات النشطة مع فئاتها ويعرضها في صفحة الخدمات
     * بناءً على PH03-TASK-034 و MOD-SERVICE-MGMT-FEAT-001
     *
     * @return Renderable
     */
    public function index()
    {
        try {
            // جلب الخدمات النشطة مع فئاتها
            $services = Service::active()
                              ->withCategory()
                              ->with('media')
                              ->orderBy('created_at', 'desc')
                              ->get();

            // جلب فئات الخدمات النشطة للفلترة (اختياري)
            $categories = ServiceCategory::active()
                                        ->ordered()
                                        ->withCount(['services' => function ($query) {
                                            $query->where('status', true);
                                        }])
                                        ->having('services_count', '>', 0)
                                        ->get();

            return view('servicemanagement::site.services.index', compact('services', 'categories'));

        } catch (\Exception $e) {
            // في حالة حدوث خطأ، عرض صفحة فارغة مع رسالة
            return view('servicemanagement::site.services.index', [
                'services' => collect(),
                'categories' => collect(),
                'error' => 'حدث خطأ في تحميل الخدمات. يرجى المحاولة مرة أخرى.'
            ]);
        }
    }

    /**
     * عرض تفاصيل خدمة محددة
     *
     * @param int $id معرف الخدمة
     * @return Renderable
     */
    public function show($id)
    {
        try {
            $service = Service::active()
                             ->withCategory()
                             ->with('media')
                             ->findOrFail($id);

            // جلب خدمات مشابهة من نفس الفئة
            $relatedServices = Service::active()
                                     ->where('id', '!=', $id)
                                     ->where('category_id', $service->category_id)
                                     ->with('media')
                                     ->limit(3)
                                     ->get();

            return view('servicemanagement::site.services.show', compact('service', 'relatedServices'));

        } catch (\Exception $e) {
            abort(404, 'الخدمة المطلوبة غير موجودة');
        }
    }

    /**
     * معالجة تقديم طلب خدمة جديد
     *
     * المهمة: PH03-TASK-037 - BE-LOGIC-SITE-SERVICE-REQUEST-SUBMISSION-001
     * يتحقق من صحة المدخلات، ينشئ سجل ServiceRequest، يرسل إشعاراً للإدارة
     *
     * @param ServiceRequestRequest $request
     * @return JsonResponse|RedirectResponse
     */
    public function storeRequest(ServiceRequestRequest $request)
    {
        try {
            // التحقق من عدم وجود طلب مشابه حديث
            if ($request->hasDuplicateRequest()) {
                if (request()->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'لقد قمت بتقديم طلب مشابه مؤخراً. يرجى انتظار ساعة واحدة قبل تقديم طلب جديد.',
                    ], 422);
                }

                return redirect()->back()
                    ->withInput()
                    ->withErrors(['duplicate' => 'لقد قمت بتقديم طلب مشابه مؤخراً. يرجى انتظار ساعة واحدة قبل تقديم طلب جديد.']);
            }

            // بدء معاملة قاعدة البيانات
            DB::beginTransaction();

            // إنشاء طلب الخدمة
            $serviceRequest = ServiceRequest::create($request->getServiceRequestData());

            // تحميل العلاقات
            $serviceRequest->load(['service', 'user']);

            // إرسال إشعار للإدارة
            $this->notifyAdmins($serviceRequest);

            // تأكيد المعاملة
            DB::commit();

            // تسجيل العملية في السجل
            Log::info('تم إنشاء طلب خدمة جديد', [
                'service_request_id' => $serviceRequest->id,
                'service_id' => $serviceRequest->service_id,
                'customer_name' => $serviceRequest->customer_name,
                'customer_phone' => $serviceRequest->customer_phone,
                'user_id' => $serviceRequest->user_id,
            ]);

            // الاستجابة بناءً على نوع الطلب
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم استلام طلبك بنجاح! سنتواصل معك خلال 24 ساعة.',
                    'data' => [
                        'request_number' => $serviceRequest->formatted_request_number,
                        'service_name' => $serviceRequest->service->name,
                        'customer_name' => $serviceRequest->customer_name,
                    ],
                ], 201);
            }

            return redirect()->back()
                ->with('success', 'تم استلام طلبك بنجاح! سنتواصل معك خلال 24 ساعة.')
                ->with('request_number', $serviceRequest->formatted_request_number);

        } catch (\Exception $e) {
            // التراجع عن المعاملة في حالة الخطأ
            DB::rollBack();

            // تسجيل الخطأ
            Log::error('خطأ في إنشاء طلب الخدمة', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->validated(),
            ]);

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.',
                ], 500);
            }

            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.']);
        }
    }

    /**
     * إرسال إشعارات للإدارة بطلب الخدمة الجديد
     *
     * @param ServiceRequest $serviceRequest
     * @return void
     */
    private function notifyAdmins(ServiceRequest $serviceRequest)
    {
        try {
            // الحصول على المديرين والموظفين المخولين
            $admins = User::whereHas('roles', function ($query) {
                $query->whereIn('name', ['Super Admin', 'Employee']);
            })->get();

            // إرسال الإشعار لكل مدير/موظف
            if ($admins->isNotEmpty()) {
                Notification::send($admins, new NewServiceRequestNotification($serviceRequest));
            }

            // يمكن إضافة إشعارات إضافية هنا (SMS، Slack، إلخ)

        } catch (\Exception $e) {
            // تسجيل خطأ الإشعار دون إيقاف العملية الرئيسية
            Log::warning('فشل في إرسال إشعار طلب الخدمة', [
                'service_request_id' => $serviceRequest->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
