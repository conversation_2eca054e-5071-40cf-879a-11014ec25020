# وحدة إدارة العروض الترويجية - PromotionManagement Module

## نظرة عامة

وحدة إدارة العروض الترويجية هي جزء من نظام MotorLine لإدارة معارض السيارات. تتولى هذه الوحدة إدارة العروض الترويجية على السيارات وعرضها في الموقع العام.

## الميزات المنفذة

### 1. عرض قائمة العروض الترويجية في الموقع العام
- **المهمة**: `PH03-TASK-038` - `BE-CTRL-SITE-PROMOTIONS-LIST-DISPLAY-001`
- **الهدف**: إنشاء Controller action لعرض قائمة العروض الترويجية النشطة
- **الميزات**:
  - عرض العروض النشطة والجارية فقط
  - فلترة العروض حسب التاريخ والحالة
  - عرض صور بنرات العروض
  - ترقيم النتائج (10 عناصر لكل صفحة)
  - تصميم متجاوب
  - حالة فارغة عند عدم وجود عروض

### 2. واجهة عرض قائمة العروض الترويجية (Blade View)
- **المهمة**: `PH03-TASK-039` - `FE-BLADE-SITE-PROMOTIONS-LIST-VIEW-001`
- **الهدف**: إنشاء ملف Blade view لعرض قائمة العروض الترويجية في الموقع العام
- **الميزات**:
  - عنوان "أحدث العروض الترويجية"
  - عرض بطاقات العروض مع صور البنرات
  - استخدام `getFirstMediaUrl('promotion_banners')` لجلب الصور
  - placeholder image عند عدم وجود صورة
  - روابط صحيحة لصفحة تفاصيل العرض
  - ترقيم باستخدام Bootstrap 5
  - حالة فارغة مناسبة عند عدم وجود عروض

### 3. عرض تفاصيل العرض الترويجي
- **المهمة**: `PH03-TASK-040` - `BE-CTRL-SITE-PROMOTION-DETAIL-DISPLAY-001`
- **الهدف**: إنشاء Controller action وview لعرض تفاصيل عرض ترويجي محدد
- **الميزات**:
  - عرض تفاصيل العرض مع صورة البنر
  - عرض السيارات المشمولة بالعرض
  - عرض أسعار العروض الخاصة
  - Route Model Binding للأمان
  - التحقق من صحة العرض وحالته

## الهيكل التقني

### Models
- **Promotion**: نموذج العروض الترويجية
  - دعم الترجمة باستخدام `spatie/laravel-translatable`
  - دعم الصور باستخدام `spatie/laravel-medialibrary`
  - علاقة كثير لكثير مع السيارات
  - نطاقات للفلترة (active, current, activeAndCurrent)
  - خصائص محسوبة للصور والتواريخ

### Controllers
- **SitePromotionController**: Controller الموقع العام
  - دالة `index()` لعرض قائمة العروض النشطة
  - دالة `show(Promotion $promotion)` لعرض تفاصيل عرض محدد
  - استخدام eager loading للأداء
  - ترقيم النتائج
  - Route Model Binding للأمان

### Views
- **site/promotions/index.blade.php**: صفحة قائمة العروض
- **site/promotions/show.blade.php**: صفحة تفاصيل العرض

### Database
- **promotions**: جدول العروض الترويجية
- **car_promotion**: جدول ربط السيارات بالعروض (pivot table)

## المسارات

### مسارات الموقع العام
```php
// عرض قائمة العروض الترويجية
GET /promotions

// عرض تفاصيل عرض ترويجي محدد
GET /promotions/{promotion}
```

## التثبيت والإعداد

### 1. تشغيل Migrations
```bash
php artisan migrate --path=Modules/PromotionManagement/Database/Migrations
```

### 2. تفعيل الموديول
```bash
php artisan module:enable PromotionManagement
```

## الاستخدام

### إضافة عرض ترويجي جديد
```php
use Modules\PromotionManagement\Models\Promotion;

$promotion = Promotion::create([
    'name' => ['ar' => 'عرض نهاية العام'],
    'description' => ['ar' => 'خصومات هائلة على جميع السيارات'],
    'start_date' => now(),
    'end_date' => now()->addDays(30),
    'status' => true,
]);
```

### إضافة صورة بنر للعرض
```php
$promotion->addMediaFromRequest('banner')
        ->toMediaCollection('promotion_banners');
```

### جلب العروض النشطة والجارية
```php
$promotions = Promotion::activeAndCurrent()
                      ->with('media')
                      ->orderBy('created_at', 'desc')
                      ->paginate(10);
```

### ربط سيارة بعرض ترويجي
```php
$promotion->cars()->attach($carId, [
    'car_offer_price' => 95000.00
]);
```

## المتطلبات

### الحزم المطلوبة
- `spatie/laravel-translatable`: للترجمة
- `spatie/laravel-medialibrary`: لإدارة الصور
- `nwidart/laravel-modules`: لإدارة الموديولات

### إعدادات قاعدة البيانات
- دعم JSON للترجمة
- دعم Foreign Keys
- فهارس للأداء على حقول التاريخ والحالة

## الأمان

### الحماية المطبقة
- عرض العروض النشطة والجارية فقط
- التحقق من صحة التواريخ
- حماية من SQL Injection

## الأداء

### التحسينات المطبقة
- استخدام Eager Loading للصور
- فهرسة الحقول المهمة (status, start_date, end_date)
- فهرس مركب للاستعلامات المتكررة
- ترقيم النتائج

### التخزين المؤقت
- يمكن إضافة تخزين مؤقت للعروض النشطة مستقبلاً

## الميزات القادمة

### المرحلة التالية
1. **إدارة العروض**: لوحة تحكم لإدارة العروض
2. **ربط العروض بالسيارات**: واجهة لربط السيارات بالعروض
3. **إحصائيات العروض**: تقارير عن أداء العروض
4. **API للعروض**: نقاط نهاية API للتطبيق المحمول

### تحسينات مستقبلية
- نظام إشعارات انتهاء العروض
- عروض مخصصة للعملاء
- تكامل مع نظام الدفع
- عروض محدودة الكمية

## هيكل الملفات
```
Modules/PromotionManagement/
├── Config/
├── Database/
│   ├── Migrations/
│   └── Seeders/
├── Http/
│   └── Controllers/
│       └── Site/
├── Models/
├── Resources/
│   └── views/
│       └── site/
│           └── promotions/
└── Routes/
```

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
