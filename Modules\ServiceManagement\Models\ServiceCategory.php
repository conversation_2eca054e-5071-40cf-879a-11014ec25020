<?php

namespace Modules\ServiceManagement\Models;

use Modules\Core\Models\BaseModel;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

/**
 * ServiceCategory Model
 *
 * يمثل هذا النموذج جدول فئات الخدمات في النظام
 * بناءً على مواصفات TS-FR.md (DB-TBL-014)
 *
 * @property int $id
 * @property string $name اسم فئة الخدمة
 * @property string|null $description وصف فئة الخدمة
 * @property bool $status حالة فئة الخدمة (نشطة/غير نشطة)
 * @property int $display_order ترتيب العرض
 * @property \Illuminate\Support\Carbon|null $created_at تاريخ الإنشاء
 * @property \Illuminate\Support\Carbon|null $updated_at تاريخ التحديث
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\ServiceManagement\Models\Service[] $services علاقة الخدمات
 */
class ServiceCategory extends BaseModel implements HasMedia
{
    use InteractsWithMedia;
    use HasTranslations;

    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'status',
        'display_order',
    ];

    /**
     * الخصائص التي يمكن ترجمتها
     *
     * @var array
     */
    public $translatable = [
        'name',
        'description',
    ];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة
     *
     * @var array
     */
    protected $casts = [
        'status' => 'boolean',
        'display_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * علاقة الخدمات المرتبطة بهذه الفئة
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function services()
    {
        return $this->hasMany(Service::class, 'category_id');
    }

    /**
     * تسجيل مجموعات الوسائط
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('service_category_icons')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/svg+xml', 'image/webp']);
    }

    /**
     * نطاق للحصول على الفئات النشطة فقط
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * نطاق للترتيب حسب ترتيب العرض
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order', 'asc');
    }

    /**
     * مصنع النموذج
     *
     * @return \Modules\ServiceManagement\Database\factories\ServiceCategoryFactory
     */
    protected static function newFactory()
    {
        return \Modules\ServiceManagement\Database\factories\ServiceCategoryFactory::new();
    }
}
