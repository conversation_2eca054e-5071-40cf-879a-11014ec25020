<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    
    <title><?php echo $__env->yieldContent('title', 'موتور لاين - معرض السيارات الجديدة'); ?></title>

    
    <?php
        $metaDescription = trim($__env->yieldContent('meta_description')) ?: 'موتور لاين - وجهتك الأولى لشراء السيارات الجديدة بأفضل الأسعار وأعلى جودة خدمة في المملكة العربية السعودية';
        $metaKeywords = trim($__env->yieldContent('meta_keywords')) ?: 'سيارات جديدة، معرض سيارات، شراء سيارة، تمويل سيارات، السعودية، الرياض';
        $ogTitle = trim($__env->yieldContent('og_title')) ?: (trim($__env->yieldContent('title')) ?: 'موتور لاين - معرض السيارات الجديدة');
        $ogDescription = trim($__env->yieldContent('og_description')) ?: $metaDescription;
        $ogImage = trim($__env->yieldContent('og_image')) ?: asset('images/logo-og.jpg');
        $ogUrl = trim($__env->yieldContent('og_url')) ?: url()->current();
        $ogType = trim($__env->yieldContent('og_type')) ?: 'website';
    ?>

    <meta name="description" content="<?php echo e($metaDescription); ?>">
    <meta name="keywords" content="<?php echo e($metaKeywords); ?>">
    <meta name="author" content="موتور لاين">

    
    <meta property="og:title" content="<?php echo e($ogTitle); ?>">
    <meta property="og:description" content="<?php echo e($ogDescription); ?>">
    <meta property="og:image" content="<?php echo e($ogImage); ?>">
    <meta property="og:url" content="<?php echo e($ogUrl); ?>">
    <meta property="og:type" content="<?php echo e($ogType); ?>">
    <meta property="og:site_name" content="موتور لاين">
    <meta property="og:locale" content="ar_SA">

    
    <?php
        $twitterCard = trim($__env->yieldContent('twitter_card')) ?: 'summary_large_image';
        $twitterTitle = trim($__env->yieldContent('twitter_title')) ?: $ogTitle;
        $twitterDescription = trim($__env->yieldContent('twitter_description')) ?: $ogDescription;
        $twitterImage = trim($__env->yieldContent('twitter_image')) ?: $ogImage;
    ?>

    <meta name="twitter:card" content="<?php echo e($twitterCard); ?>">
    <meta name="twitter:title" content="<?php echo e($twitterTitle); ?>">
    <meta name="twitter:description" content="<?php echo e($twitterDescription); ?>">
    <meta name="twitter:image" content="<?php echo e($twitterImage); ?>">

    
    <?php echo $__env->yieldPushContent('meta_tags'); ?>



    
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo e(asset('images/apple-touch-icon.png')); ?>">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo e(asset('images/favicon-32x32.png')); ?>">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo e(asset('images/favicon-16x16.png')); ?>">

    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
          crossorigin="anonymous" referrerpolicy="no-referrer">

    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet"
          integrity="sha384-PJsj/BTMqILvmcej7ulplguok8ag4xFTPryRq8xevL7eBYSmpXKcbNVuy+P0RMgq"
          crossorigin="anonymous">

    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/site_app.css']); ?>

    
    <link rel="stylesheet" href="<?php echo e(asset('css/brand-identity.css')); ?>">

    
    <?php echo $__env->yieldPushContent('styles'); ?>

    
    <style>
        /* إعدادات عامة للموقع */
        :root {
            --font-family-arabic: 'IBM Plex Sans Arabic', sans-serif;
        }

        * {
            font-family: var(--font-family-arabic);
        }

        body {
            background-color: var(--light-bg);
            color: var(--text-color);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            flex: 1;
            padding-top: 2rem;
            padding-bottom: 2rem;
        }

        /* تحسينات للـ RTL */
        .rtl-fix {
            direction: rtl;
            text-align: right;
        }

        /* تحسينات الأداء */
        img {
            max-width: 100%;
            height: auto;
        }

        /* تحسينات الوصولية */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* تحسينات للطباعة */
        @media print {
            .site-header,
            .site-footer,
            .btn,
            .navbar {
                display: none !important;
            }

            .main-content {
                padding: 0;
            }
        }

        /* Loading States */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--secondary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Scroll to Top Button */
        .scroll-to-top {
            position: fixed;
            bottom: 30px;
            left: 30px;
            width: 50px;
            height: 50px;
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 50%;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all var(--transition-speed) ease;
            z-index: 1000;
            box-shadow: var(--box-shadow);
        }

        .scroll-to-top:hover {
            background-color: var(--accent-color);
            transform: translateY(-3px);
        }

        .scroll-to-top.show {
            display: flex;
        }
    </style>
</head>

<body>
    
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
    </div>

    
    <?php echo $__env->make('site.layouts.partials._header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    
    <main class="main-content">
        
        <?php if(session('success')): ?>
            <div class="container">
                <div class="alert brand-alert brand-alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                </div>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="container">
                <div class="alert brand-alert brand-alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo e(session('error')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                </div>
            </div>
        <?php endif; ?>

        <?php if(session('warning')): ?>
            <div class="container">
                <div class="alert brand-alert brand-alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo e(session('warning')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                </div>
            </div>
        <?php endif; ?>

        <?php if(session('info')): ?>
            <div class="container">
                <div class="alert brand-alert brand-alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <?php echo e(session('info')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                </div>
            </div>
        <?php endif; ?>

        
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    
    <?php echo $__env->make('site.layouts.partials._footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    
    <button class="scroll-to-top" id="scrollToTop" title="العودة للأعلى">
        <i class="fas fa-chevron-up"></i>
    </button>

    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz"
            crossorigin="anonymous"></script>

    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/site_app.js']); ?>

    
    <?php echo $__env->yieldPushContent('scripts'); ?>

    
    <script>
        // إعدادات عامة
        document.addEventListener('DOMContentLoaded', function() {
            // زر العودة للأعلى
            const scrollToTopBtn = document.getElementById('scrollToTop');

            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    scrollToTopBtn.classList.add('show');
                } else {
                    scrollToTopBtn.classList.remove('show');
                }
            });

            scrollToTopBtn.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            // إخفاء التنبيهات تلقائياً بعد 5 ثوانٍ
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });

            // تحسين تجربة المستخدم للنماذج
            const forms = document.querySelectorAll('form');
            forms.forEach(function(form) {
                form.addEventListener('submit', function() {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
                    }
                });
            });
        });

        // دوال مساعدة عامة
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        function showToast(message, type = 'success') {
            // يمكن تطوير نظام Toast مخصص هنا
            console.log(`${type}: ${message}`);
        }
    </script>
</body>
</html>
<?php /**PATH D:\Project\MotorLine_10\resources\views/site/layouts/site_layout.blade.php ENDPATH**/ ?>