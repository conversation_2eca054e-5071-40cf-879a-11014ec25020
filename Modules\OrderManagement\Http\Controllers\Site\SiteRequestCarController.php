<?php

namespace Modules\OrderManagement\Http\Controllers\Site;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Modules\CarCatalog\Models\Brand;
use Modules\CarCatalog\Models\CarModel;
use Modules\CarCatalog\Models\ManufacturingYear;
use Modules\OrderManagement\Models\Order;

/**
 * وحدة تحكم طلب السيارة المخصصة في الموقع العام
 *
 * تتعامل هذه الوحدة مع عملية "اطلب سيارتك" متعددة الخطوات
 * حيث يمكن للعملاء طلب سيارة بمواصفات مخصصة غير متوفرة في المخزون
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024
 */
class SiteRequestCarController extends Controller
{
    /**
     * الخطوة الأولى: اختيار الماركة
     *
     * @return View
     */
    public function step1(): View
    {
        // جلب الماركات النشطة
        $brands = Brand::where('status', true)
            ->orderBy('name')
            ->get();

        // إعادة تعيين بيانات الجلسة للبدء من جديد
        session()->forget('request_car_data');

        return view('ordermanagement::site.request_car.step1', compact('brands'));
    }

    /**
     * الخطوة الثانية: اختيار الموديل
     *
     * @param int $brandId
     * @return View|RedirectResponse
     */
    public function step2(int $brandId): View|RedirectResponse
    {
        // التحقق من صحة الماركة
        $brand = Brand::where('status', true)->findOrFail($brandId);

        // جلب الموديلات الخاصة بالماركة
        $models = CarModel::where('brand_id', $brandId)
            ->where('status', true)
            ->orderBy('name')
            ->get();

        if ($models->isEmpty()) {
            return redirect()->route('site.request-car.step1')
                ->with('error', 'لا توجد موديلات متاحة لهذه الماركة');
        }

        // حفظ بيانات الخطوة الأولى في الجلسة
        session(['request_car_data.brand_id' => $brandId]);
        session(['request_car_data.brand_name' => $brand->name]);

        return view('ordermanagement::site.request_car.step2', compact('brand', 'models'));
    }

    /**
     * الخطوة الثالثة: اختيار السنة
     *
     * @param int $modelId
     * @return View|RedirectResponse
     */
    public function step3(int $modelId): View|RedirectResponse
    {
        // التحقق من صحة الموديل والماركة
        $brandId = session('request_car_data.brand_id');
        if (!$brandId) {
            return redirect()->route('site.request-car.step1')
                ->with('error', 'يرجى البدء من الخطوة الأولى');
        }

        $model = CarModel::where('id', $modelId)
            ->where('brand_id', $brandId)
            ->where('status', true)
            ->firstOrFail();

        // جلب سنوات الصنع المتاحة
        $years = ManufacturingYear::where('status', true)
            ->orderBy('year', 'desc')
            ->get();

        // حفظ بيانات الخطوة الثانية في الجلسة
        session(['request_car_data.model_id' => $modelId]);
        session(['request_car_data.model_name' => $model->name]);

        return view('ordermanagement::site.request_car.step3', compact('model', 'years'));
    }

    /**
     * الخطوة الرابعة: تفاصيل إضافية ومعلومات الاتصال
     *
     * @param int $yearId
     * @return View|RedirectResponse
     */
    public function step4(int $yearId): View|RedirectResponse
    {
        // التحقق من صحة البيانات السابقة
        $requestData = session('request_car_data');
        if (!$requestData || !isset($requestData['brand_id'], $requestData['model_id'])) {
            return redirect()->route('site.request-car.step1')
                ->with('error', 'يرجى البدء من الخطوة الأولى');
        }

        // التحقق من صحة السنة
        $year = ManufacturingYear::where('status', true)->findOrFail($yearId);

        // حفظ بيانات الخطوة الثالثة في الجلسة
        session(['request_car_data.year_id' => $yearId]);
        session(['request_car_data.year' => $year->year]);

        // جلب البيانات المحفوظة لعرضها
        $brand = Brand::find($requestData['brand_id']);
        $model = CarModel::find($requestData['model_id']);

        return view('ordermanagement::site.request_car.step4', compact('brand', 'model', 'year'));
    }

    /**
     * معالجة تقديم طلب السيارة المخصصة
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function submitRequest(Request $request): JsonResponse
    {
        try {
            // التحقق من بيانات الجلسة
            $requestData = session('request_car_data');
            if (!$requestData || !isset($requestData['brand_id'], $requestData['model_id'], $requestData['year_id'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'انتهت صلاحية الجلسة. يرجى البدء من جديد.',
                ], 400);
            }

            // التحقق من صحة البيانات
            $validatedData = $request->validate([
                'customer_name' => 'required|string|max:100',
                'customer_phone' => 'required|string|regex:/^(05|5)[0-9]{8}$/',
                'customer_email' => 'nullable|email|max:150',
                'preferred_color' => 'nullable|string|max:50',
                'additional_features' => 'nullable|string|max:1000',
                'budget_range' => 'nullable|string|max:50',
                'delivery_preference' => 'nullable|string|max:100',
                'notes' => 'nullable|string|max:2000',
            ]);

            // إنشاء طلب السيارة المخصصة
            $order = Order::create([
                'order_type' => 'custom_car_request',
                'status' => 'pending',
                'user_id' => auth()->id(), // null إذا لم يكن مسجل دخول

                // بيانات العميل
                'customer_name' => $validatedData['customer_name'],
                'customer_phone' => $validatedData['customer_phone'],
                'customer_email' => $validatedData['customer_email'],

                // بيانات السيارة المطلوبة
                'requested_brand_id' => $requestData['brand_id'],
                'requested_model_id' => $requestData['model_id'],
                'requested_year_id' => $requestData['year_id'],
                'preferred_color' => $validatedData['preferred_color'],
                'additional_features' => $validatedData['additional_features'],
                'budget_range' => $validatedData['budget_range'],
                'delivery_preference' => $validatedData['delivery_preference'],
                'notes' => $validatedData['notes'],

                // تواريخ
                'order_date' => now(),
            ]);

            // تسجيل الطلب في اللوج
            Log::info('تم استلام طلب سيارة مخصصة جديد', [
                'order_id' => $order->id,
                'customer_name' => $validatedData['customer_name'],
                'customer_phone' => $validatedData['customer_phone'],
                'brand' => $requestData['brand_name'] ?? 'غير محدد',
                'model' => $requestData['model_name'] ?? 'غير محدد',
                'year' => $requestData['year'] ?? 'غير محدد',
            ]);

            // مسح بيانات الجلسة
            session()->forget('request_car_data');

            return response()->json([
                'success' => true,
                'message' => 'تم إرسال طلبك بنجاح! سنقوم بالتواصل معك في أقرب وقت ممكن.',
                'order_id' => $order->id,
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'يرجى التحقق من البيانات المدخلة',
                'errors' => $e->errors(),
            ], 422);

        } catch (\Exception $e) {
            Log::error('خطأ في تقديم طلب السيارة المخصصة: ' . $e->getMessage(), [
                'request_data' => $request->all(),
                'session_data' => session('request_car_data'),
                'exception' => $e,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال طلبك. يرجى المحاولة مرة أخرى.',
            ], 500);
        }
    }

    /**
     * جلب الموديلات حسب الماركة (AJAX)
     *
     * @param int $brandId
     * @return JsonResponse
     */
    public function getModelsByBrand(int $brandId): JsonResponse
    {
        try {
            $models = CarModel::where('brand_id', $brandId)
                ->where('status', true)
                ->orderBy('name')
                ->get(['id', 'name']);

            return response()->json([
                'success' => true,
                'models' => $models,
            ]);

        } catch (\Exception $e) {
            Log::error('خطأ في جلب الموديلات: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب الموديلات',
            ], 500);
        }
    }
}
