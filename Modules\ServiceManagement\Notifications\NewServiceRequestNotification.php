<?php

namespace Modules\ServiceManagement\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Modules\ServiceManagement\Models\ServiceRequest;

/**
 * إشعار طلب خدمة جديد للإدارة
 *
 * المهمة: PH03-TASK-037 - BE-LOGIC-SITE-SERVICE-REQUEST-SUBMISSION-001
 * يتم إرسال هذا الإشعار للإدارة عند وصول طلب خدمة جديد
 */
class NewServiceRequestNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * طلب الخدمة
     *
     * @var ServiceRequest
     */
    protected $serviceRequest;

    /**
     * إنشاء إشعار جديد
     *
     * @param ServiceRequest $serviceRequest
     */
    public function __construct(ServiceRequest $serviceRequest)
    {
        $this->serviceRequest = $serviceRequest;
    }

    /**
     * تحديد قنوات الإشعار
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * الحصول على تمثيل البريد الإلكتروني للإشعار
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $serviceRequest = $this->serviceRequest;
        $service = $serviceRequest->service;

        return (new MailMessage)
            ->subject('طلب خدمة جديد - ' . $service->name)
            ->greeting('مرحباً!')
            ->line('تم استلام طلب خدمة جديد من الموقع الإلكتروني.')
            ->line('**تفاصيل الطلب:**')
            ->line('رقم الطلب: ' . $serviceRequest->formatted_request_number)
            ->line('الخدمة المطلوبة: ' . $service->name)
            ->line('اسم العميل: ' . $serviceRequest->customer_name)
            ->line('رقم الجوال: ' . $serviceRequest->customer_phone)
            ->line('البريد الإلكتروني: ' . ($serviceRequest->customer_email ?: 'غير محدد'))
            ->when($serviceRequest->notes, function ($mail) use ($serviceRequest) {
                return $mail->line('ملاحظات العميل: ' . $serviceRequest->notes);
            })
            ->line('تاريخ الطلب: ' . $serviceRequest->arabic_created_at)
            ->action('عرض تفاصيل الطلب', url('/admin/service-requests/' . $serviceRequest->id))
            ->line('يرجى التواصل مع العميل في أقرب وقت ممكن.')
            ->salutation('مع تحيات فريق موتور لاين');
    }

    /**
     * الحصول على تمثيل قاعدة البيانات للإشعار
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toDatabase($notifiable)
    {
        $serviceRequest = $this->serviceRequest;
        $service = $serviceRequest->service;

        return [
            'type' => 'new_service_request',
            'title' => 'طلب خدمة جديد',
            'message' => 'طلب خدمة جديد من ' . $serviceRequest->customer_name . ' للخدمة: ' . $service->name,
            'service_request_id' => $serviceRequest->id,
            'service_id' => $service->id,
            'customer_name' => $serviceRequest->customer_name,
            'customer_phone' => $serviceRequest->customer_phone,
            'service_name' => $service->name,
            'request_number' => $serviceRequest->formatted_request_number,
            'action_url' => url('/admin/service-requests/' . $serviceRequest->id),
            'icon' => 'fas fa-concierge-bell',
            'color' => 'info',
        ];
    }

    /**
     * الحصول على معرف فريد للإشعار (لتجنب التكرار)
     *
     * @return string
     */
    public function uniqueId()
    {
        return 'service_request_' . $this->serviceRequest->id;
    }
}
