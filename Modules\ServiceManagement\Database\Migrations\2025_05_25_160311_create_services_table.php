<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateServicesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('category_id')->nullable(); // فئة الخدمة
            $table->json('name'); // للترجمة باستخدام spatie/laravel-translatable
            $table->json('description')->nullable(); // للترجمة باستخدام spatie/laravel-translatable
            $table->decimal('price', 10, 2); // سعر الخدمة
            $table->string('currency', 3)->default('SAR'); // العملة
            $table->boolean('status')->default(true); // حالة الخدمة (نشطة/غير نشطة)
            $table->timestamps();
            $table->softDeletes();

            // المفاتيح الخارجية
            $table->foreign('category_id')
                  ->references('id')
                  ->on('service_categories')
                  ->onDelete('set null')
                  ->onUpdate('cascade');

            // الفهارس
            $table->index('category_id');
            $table->index('status');
            $table->index('price');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('services');
    }
}
