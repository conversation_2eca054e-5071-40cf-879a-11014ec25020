<?php $__env->startSection('title', 'العروض الترويجية - موتور لاين'); ?>
<?php $__env->startSection('meta_description', 'تصفح أحدث العروض الترويجية على السيارات في موتور لاين'); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4 text-center">أحدث العروض الترويجية</h1>
            <p class="text-center text-muted mb-5">اكتشف أفضل العروض والخصومات على السيارات</p>
        </div>
    </div>

    <?php if($promotions->count() > 0): ?>
        <div class="row">
            <?php $__currentLoopData = $promotions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $promotion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <img src="<?php echo e($promotion->getFirstMediaUrl('promotion_banners') ?: asset('placeholder.jpg')); ?>"
                             class="card-img-top"
                             alt="<?php echo e($promotion->name); ?>"
                             style="height: 250px; object-fit: cover;"
                             onerror="this.src='<?php echo e(asset('placeholder.jpg')); ?>'">

                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title"><?php echo e($promotion->name); ?></h5>

                            <?php if($promotion->description): ?>
                                <p class="card-text text-muted"><?php echo e(Str::limit($promotion->description, 100)); ?></p>
                            <?php endif; ?>

                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar-alt me-1"></i>
                                        ينتهي في <?php echo e($promotion->end_date->format('Y/m/d')); ?>

                                    </small>
                                </div>

                                <a href="<?php echo e(route('site.promotions.show', $promotion->id)); ?>" class="btn btn-primary w-100">
                                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-center">
                    <?php echo e($promotions->links('pagination::bootstrap-5')); ?>

                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Empty State -->
        <div class="row">
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-4x text-muted mb-3"></i>
                    <h3 class="text-muted">لا توجد عروض متاحة حالياً</h3>
                    <p class="text-muted">يرجى التحقق مرة أخرى قريباً للاطلاع على أحدث العروض!</p>
                    <a href="<?php echo e(route('site.home')); ?>" class="btn btn-primary mt-3">
                        <i class="fas fa-home me-2"></i>العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('site.layouts.site_layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Project\MotorLine_10\Modules/PromotionManagement\Resources/views/site/promotions/index.blade.php ENDPATH**/ ?>