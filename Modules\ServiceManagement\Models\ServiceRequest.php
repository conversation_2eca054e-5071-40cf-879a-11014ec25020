<?php

namespace Modules\ServiceManagement\Models;

use Modules\Core\Models\BaseModel;
use Modules\UserManagement\Models\User;

/**
 * ServiceRequest Model
 *
 * يمثل هذا النموذج جدول طلبات الخدمات في النظام
 * بناءً على مواصفات TS-FR.md (DB-TBL-016)
 *
 * المهمة: PH03-TASK-037 - BE-LOGIC-SITE-SERVICE-REQUEST-SUBMISSION-001
 *
 * @property int $id
 * @property int $service_id معرف الخدمة المطلوبة
 * @property int|null $user_id معرف العميل (إذا كان مسجلاً)
 * @property string $customer_name اسم العميل
 * @property string $customer_phone رقم جوال العميل
 * @property string|null $customer_email بريد العميل
 * @property string|null $notes ملاحظات إضافية من العميل
 * @property string $status حالة طلب الخدمة
 * @property string|null $admin_notes ملاحظات إدارية
 * @property \Illuminate\Support\Carbon|null $created_at تاريخ الإنشاء
 * @property \Illuminate\Support\Carbon|null $updated_at تاريخ التحديث
 * @property \Modules\ServiceManagement\Models\Service $service علاقة الخدمة
 * @property \Modules\UserManagement\Models\User|null $user علاقة المستخدم
 */
class ServiceRequest extends BaseModel
{
    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي
     *
     * @var array
     */
    protected $fillable = [
        'service_id',
        'user_id',
        'customer_name',
        'customer_phone',
        'customer_email',
        'notes',
        'status',
        'admin_notes',
    ];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة
     *
     * @var array
     */
    protected $casts = [
        'service_id' => 'integer',
        'user_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * حالات طلب الخدمة المتاحة
     */
    const STATUS_NEW = 'new';
    const STATUS_CONTACTED = 'contacted';
    const STATUS_CONFIRMED = 'confirmed';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * الحصول على جميع حالات طلب الخدمة
     *
     * @return array
     */
    public static function getStatuses()
    {
        return [
            self::STATUS_NEW => 'جديد',
            self::STATUS_CONTACTED => 'تم التواصل',
            self::STATUS_CONFIRMED => 'مؤكد',
            self::STATUS_IN_PROGRESS => 'قيد التنفيذ',
            self::STATUS_COMPLETED => 'مكتمل',
            self::STATUS_CANCELLED => 'ملغي',
        ];
    }

    /**
     * الحصول على اسم الحالة باللغة العربية
     *
     * @return string
     */
    public function getStatusNameAttribute()
    {
        $statuses = self::getStatuses();
        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * علاقة الخدمة المطلوبة
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function service()
    {
        return $this->belongsTo(Service::class, 'service_id');
    }

    /**
     * علاقة المستخدم (إذا كان مسجلاً)
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * نطاق للحصول على الطلبات الجديدة
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNew($query)
    {
        return $query->where('status', self::STATUS_NEW);
    }

    /**
     * نطاق للحصول على الطلبات النشطة (غير المكتملة أو الملغية)
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->whereNotIn('status', [self::STATUS_COMPLETED, self::STATUS_CANCELLED]);
    }

    /**
     * نطاق للحصول على الطلبات مع الخدمة والمستخدم
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithRelations($query)
    {
        return $query->with(['service', 'user']);
    }

    /**
     * الحصول على رقم الطلب المنسق
     *
     * @return string
     */
    public function getFormattedRequestNumberAttribute()
    {
        return 'SR-' . str_pad($this->id, 6, '0', STR_PAD_LEFT);
    }

    /**
     * التحقق من إمكانية تحديث الطلب
     *
     * @return bool
     */
    public function canBeUpdated()
    {
        return in_array($this->status, [self::STATUS_NEW, self::STATUS_CONTACTED, self::STATUS_CONFIRMED]);
    }

    /**
     * التحقق من إمكانية إلغاء الطلب
     *
     * @return bool
     */
    public function canBeCancelled()
    {
        return !in_array($this->status, [self::STATUS_COMPLETED, self::STATUS_CANCELLED]);
    }

    /**
     * تحديث حالة الطلب
     *
     * @param string $status
     * @param string|null $adminNotes
     * @return bool
     */
    public function updateStatus($status, $adminNotes = null)
    {
        $this->status = $status;
        if ($adminNotes) {
            $this->admin_notes = $adminNotes;
        }
        return $this->save();
    }

    /**
     * الحصول على معلومات العميل المنسقة
     *
     * @return array
     */
    public function getCustomerInfoAttribute()
    {
        return [
            'name' => $this->customer_name,
            'phone' => $this->customer_phone,
            'email' => $this->customer_email,
            'is_registered' => !is_null($this->user_id),
        ];
    }

    /**
     * الحصول على تاريخ الطلب المنسق
     *
     * @return string
     */
    public function getFormattedCreatedAtAttribute()
    {
        return $this->created_at->format('Y-m-d H:i');
    }

    /**
     * الحصول على تاريخ الطلب المنسق باللغة العربية
     *
     * @return string
     */
    public function getArabicCreatedAtAttribute()
    {
        return $this->created_at->locale('ar')->translatedFormat('j F Y - H:i');
    }
}
