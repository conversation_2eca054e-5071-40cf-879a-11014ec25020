<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCorporateEnquiriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('corporate_enquiries', function (Blueprint $table) {
            $table->bigIncrements('id');

            // بيانات الشركة
            $table->string('company_name', 200)->comment('اسم الشركة');
            $table->string('contact_person_name', 100)->comment('اسم مسؤول الاتصال');
            $table->string('email', 150)->comment('البريد الإلكتروني');
            $table->string('phone', 20)->comment('رقم الجوال');
            $table->text('request_details')->comment('تفاصيل الطلب');
            $table->string('city', 100)->nullable()->comment('المدينة');

            // حالة الطلب
            $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled'])
                  ->default('pending')
                  ->comment('حالة الطلب');

            // ملاحظات إدارية
            $table->text('admin_notes')->nullable()->comment('ملاحظات الإدارة');

            // الموظف المعين للمتابعة
            $table->unsignedBigInteger('assigned_employee_id')->nullable()->comment('معرف الموظف المعين');

            // تواريخ المتابعة
            $table->timestamp('contacted_at')->nullable()->comment('تاريخ أول اتصال');
            $table->timestamp('follow_up_date')->nullable()->comment('تاريخ المتابعة المقرر');

            $table->timestamps();
            $table->softDeletes();

            // الفهارس
            $table->index('status');
            $table->index('assigned_employee_id');
            $table->index('created_at');
            $table->index('company_name');
            $table->index('email');

            // المفاتيح الخارجية
            $table->foreign('assigned_employee_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('corporate_enquiries');
    }
}
