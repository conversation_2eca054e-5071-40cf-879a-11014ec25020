<?php

namespace Modules\CarCatalog\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Core\Models\BaseModel;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

/**
 * نموذج السيارة - Car Model.
 *
 * يمثل هذا النموذج جدول السيارات الرئيسي في النظام ويحتوي على جميع
 * المعلومات الأساسية والفنية للسيارة بما في ذلك العلاقات مع الجداول المرجعية
 *
 * <AUTHOR> Development Team
 *
 * @version 1.0.0
 *
 * @since 2024
 *
 * @property int $id المعرّف الفريد للسيارة
 * @property string $title عنوان السيارة (قابل للترجمة)
 * @property string|null $description وصف السيارة (قابل للترجمة)
 * @property int $brand_id معرّف الماركة (مفتاح خارجي)
 * @property int $car_model_id معرّف الموديل (مفتاح خارجي)
 * @property int $manufacturing_year_id معرّف سنة الصنع (مفتاح خارجي)
 * @property int $body_type_id معرّف نوع الهيكل (مفتاح خارجي)
 * @property int $transmission_type_id معرّف نوع ناقل الحركة (مفتاح خارجي)
 * @property int $fuel_type_id معرّف نوع الوقود (مفتاح خارجي)
 * @property int $main_color_id معرّف اللون الرئيسي (مفتاح خارجي)
 * @property int|null $interior_color_id معرّف لون المقصورة الداخلية (مفتاح خارجي اختياري)
 * @property float $price سعر السيارة (عدد عشري بدقة 2)
 * @property string $currency عملة السعر (SAR, USD, EUR)
 * @property int|null $mileage عدد الكيلومترات المقطوعة (اختياري)
 * @property string $mileage_unit وحدة قياس المسافة
 * @property int|null $engine_capacity سعة المحرك بالسي سي (CC)
 * @property int|null $doors_count عدد الأبواب
 * @property int|null $seats_count عدد المقاعد
 * @property string|null $vin رقم الهيكل VIN
 * @property string|null $plate_number رقم اللوحة
 * @property string $condition حالة السيارة (جديدة/مستعملة/معتمدة)
 * @property bool $is_featured سيارة مميزة
 * @property bool $is_sold تم بيعها
 * @property bool $is_active نشطة/غير نشطة
 * @property int|null $created_by معرّف المستخدم الذي أنشأ السيارة
 * @property int|null $updated_by معرّف المستخدم الذي عدل السيارة
 * @property \Illuminate\Support\Carbon|null $created_at تاريخ الإنشاء
 * @property \Illuminate\Support\Carbon|null $updated_at تاريخ التحديث
 * @property \Illuminate\Support\Carbon|null $deleted_at تاريخ الحذف الناعم
 * @property \Modules\CarCatalog\Models\Brand $brand علاقة الماركة
 * @property \Modules\CarCatalog\Models\CarModel $carModel علاقة الموديل
 * @property \Modules\CarCatalog\Models\ManufacturingYear $manufacturingYear علاقة سنة الصنع
 * @property \Modules\CarCatalog\Models\BodyType $bodyType علاقة نوع الهيكل
 * @property \Modules\CarCatalog\Models\TransmissionType $transmissionType علاقة نوع ناقل الحركة
 * @property \Modules\CarCatalog\Models\FuelType $fuelType علاقة نوع الوقود
 * @property \Modules\CarCatalog\Models\Color $mainColor علاقة اللون الرئيسي
 * @property \Modules\CarCatalog\Models\Color|null $interiorColor علاقة لون المقصورة الداخلية
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\CarCatalog\Models\CarFeature[] $features علاقة ميزات السيارة
 * @property \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection|\Spatie\MediaLibrary\MediaCollections\Models\Media[] $media مجموعة الوسائط
 */
class Car extends BaseModel implements HasMedia
{
    use HasFactory;
    use SoftDeletes;
    use InteractsWithMedia;
    use HasTranslations;
    use \App\Traits\HasStandardMediaConversions;

    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'description',
        'brand_id',
        'car_model_id',
        'manufacturing_year_id',
        'body_type_id',
        'transmission_type_id',
        'fuel_type_id',
        'main_color_id',
        'interior_color_id',
        'price',
        'currency',
        'offer_price',
        'offer_start_date',
        'offer_end_date',
        'mileage',
        'mileage_unit',
        'engine_capacity',
        'doors_count',
        'seats_count',
        'vin',
        'plate_number',
        'condition',
        'video_url',
        'is_featured',
        'is_sold',
        'is_active',
        'created_by',
        'updated_by',
    ];

    /**
     * الخصائص التي يمكن ترجمتها.
     *
     * @var array
     */
    public $translatable = [
        'title',
        'description',
    ];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة.
     *
     * @var array
     */
    protected $casts = [
        'price'            => 'decimal:2',
        'offer_price'      => 'decimal:2',
        'offer_start_date' => 'date',
        'offer_end_date'   => 'date',
        'mileage'          => 'integer',
        'engine_capacity'  => 'integer',
        'doors_count'      => 'integer',
        'seats_count'      => 'integer',
        'is_featured'      => 'boolean',
        'is_sold'          => 'boolean',
        'is_active'        => 'boolean',
        'created_at'       => 'datetime',
        'updated_at'       => 'datetime',
        'deleted_at'       => 'datetime',
    ];

    /**
     * علاقة الماركة التي تنتمي إليها هذه السيارة.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * علاقة الموديل الذي تنتمي إليه هذه السيارة.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function carModel()
    {
        return $this->belongsTo(CarModel::class);
    }

    /**
     * علاقة سنة الصنع التي تنتمي إليها هذه السيارة.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function manufacturingYear()
    {
        return $this->belongsTo(ManufacturingYear::class);
    }

    /**
     * علاقة نوع الهيكل الذي تنتمي إليه هذه السيارة.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function bodyType()
    {
        return $this->belongsTo(BodyType::class);
    }

    /**
     * علاقة نوع ناقل الحركة الذي تنتمي إليه هذه السيارة.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function transmissionType()
    {
        return $this->belongsTo(TransmissionType::class);
    }

    /**
     * علاقة نوع الوقود الذي تنتمي إليه هذه السيارة.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function fuelType()
    {
        return $this->belongsTo(FuelType::class);
    }

    /**
     * علاقة اللون الرئيسي الذي تنتمي إليه هذه السيارة.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function mainColor()
    {
        return $this->belongsTo(Color::class, 'main_color_id');
    }

    /**
     * علاقة لون المقصورة الداخلية الذي تنتمي إليه هذه السيارة.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function interiorColor()
    {
        return $this->belongsTo(Color::class, 'interior_color_id');
    }

    /**
     * علاقة ميزات السيارة.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function features()
    {
        return $this->belongsToMany(CarFeature::class, 'car_car_feature', 'car_id', 'feature_id')
            ->withPivot('value')
            ->withTimestamps();
    }

    /**
     * علاقة المستخدمين الذين أضافوا هذه السيارة للمفضلة.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function favoritedByUsers()
    {
        return $this->belongsToMany(
            \Modules\UserManagement\Models\User::class,
            'user_favorites',
            'car_id',
            'user_id'
        )->withTimestamps();
    }

    /**
     * علاقة طلبات هذه السيارة
     * علاقة واحد لمتعدد - السيارة لها العديد من الطلبات
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function orders()
    {
        return $this->hasMany(\Modules\OrderManagement\Models\Order::class);
    }

    /**
     * علاقة العروض الترويجية المرتبطة بهذه السيارة
     * علاقة كثير لكثير - السيارة يمكن أن تكون في عدة عروض
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function promotions()
    {
        return $this->belongsToMany(
            \Modules\PromotionManagement\Models\Promotion::class,
            'car_promotion'
        )->withPivot('car_offer_price')->withTimestamps();
    }

    /**
     * تسجيل مجموعات الوسائط.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('car_images')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);

        $this->addMediaCollection('car_main_image')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);

        $this->addMediaCollection('car_documents')
            ->acceptsMimeTypes(['application/pdf', 'image/jpeg', 'image/png']);
    }

    /**
     * تسجيل تحويلات الوسائط.
     *
     * @param mixed|null $media
     */
    public function registerMediaConversions($media = null): void
    {
        // استخدام التحويلات المعيارية للسيارات
        $this->registerCarImageConversions($media);
    }

    /**
     * مصنع النموذج.
     *
     * @return \Modules\CarCatalog\Database\factories\CarFactory
     */
    protected static function newFactory()
    {
        return \Modules\CarCatalog\Database\factories\CarFactory::new();
    }
}
