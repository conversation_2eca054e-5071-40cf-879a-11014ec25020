<?php

namespace Modules\CorporateSales\Http\Controllers\Site;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Modules\CorporateSales\Http\Requests\Site\CorporateEnquiryRequest;
use Modules\CorporateSales\Models\CorporateEnquiry;

/**
 * وحدة تحكم مبيعات الشركات في الموقع العام
 *
 * تتعامل هذه الوحدة مع عرض صفحة مبيعات الشركات ومعالجة طلبات الشركات
 * من الزوار في الموقع العام بدون الحاجة لتسجيل الدخول
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024
 */
class SiteCorporateSalesController extends Controller
{
    /**
     * عرض صفحة مبيعات الشركات
     *
     * تعرض هذه الصفحة معلومات ترويجية عن خدمات مبيعات الشركات
     * ونموذج لتقديم طلب شراء أسطول سيارات للشركات والمؤسسات
     *
     * @return View
     */
    public function index(): View
    {
        // جلب المدن السعودية للقائمة المنسدلة
        $cities = $this->getSaudiCities();

        return view('corporatesales::site.index', compact('cities'));
    }

    /**
     * معالجة تقديم طلب مبيعات الشركات
     *
     * يستقبل بيانات النموذج، يتحقق من صحتها، ينشئ سجل طلب جديد،
     * يرسل إشعارات للإدارة، ويعيد استجابة نجاح للواجهة الأمامية
     *
     * @param CorporateEnquiryRequest $request
     * @return JsonResponse
     */
    public function submitRequest(CorporateEnquiryRequest $request): JsonResponse
    {
        try {
            // إنشاء سجل طلب الشركة الجديد
            $validatedData = $request->validated();
            $validatedData['status'] = CorporateEnquiry::STATUS_PENDING;

            $enquiry = CorporateEnquiry::create($validatedData);

            // إرسال إشعار للإدارة عن الطلب الجديد
            $this->sendAdminNotification($enquiry);

            // إرسال بريد إلكتروني تأكيدي للعميل (اختياري)
            $this->sendCustomerConfirmationEmail($enquiry);

            return response()->json([
                'success' => true,
                'message' => 'تم إرسال طلبك بنجاح! سنقوم بالتواصل معك في أقرب وقت ممكن.',
                'enquiry_id' => $enquiry->id,
            ]);

        } catch (\Exception $e) {
            // تسجيل الخطأ
            Log::error('خطأ في تقديم طلب مبيعات الشركات: ' . $e->getMessage(), [
                'request_data' => $request->validated(),
                'exception' => $e,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال طلبك. يرجى المحاولة مرة أخرى.',
            ], 500);
        }
    }

    /**
     * إرسال إشعار للإدارة عن طلب جديد
     *
     * @param CorporateEnquiry $enquiry
     * @return void
     */
    private function sendAdminNotification(CorporateEnquiry $enquiry): void
    {
        try {
            // تسجيل الطلب في اللوج للمتابعة
            Log::info('تم استلام طلب مبيعات شركات جديد', [
                'enquiry_id' => $enquiry->id,
                'company_name' => $enquiry->company_name,
                'contact_person' => $enquiry->contact_person_name,
                'email' => $enquiry->email,
                'phone' => $enquiry->phone,
            ]);

            // TODO: إضافة نظام الإشعارات لاحقاً
            // سيتم تطوير نظام الإشعارات في مرحلة لاحقة

        } catch (\Exception $e) {
            Log::error('خطأ في إرسال إشعار طلب مبيعات الشركات: ' . $e->getMessage());
        }
    }

    /**
     * إرسال بريد إلكتروني تأكيدي للعميل
     *
     * @param CorporateEnquiry $enquiry
     * @return void
     */
    private function sendCustomerConfirmationEmail(CorporateEnquiry $enquiry): void
    {
        try {
            // TODO: إضافة نظام البريد الإلكتروني لاحقاً
            // سيتم تطوير قوالب البريد الإلكتروني في مرحلة لاحقة
            Log::info('تم إرسال بريد تأكيد للعميل', [
                'enquiry_id' => $enquiry->id,
                'email' => $enquiry->email,
            ]);

        } catch (\Exception $e) {
            Log::error('خطأ في إرسال بريد التأكيد للعميل: ' . $e->getMessage());
        }
    }

    /**
     * الحصول على قائمة المدن السعودية
     *
     * @return array
     */
    private function getSaudiCities(): array
    {
        return [
            'الرياض',
            'جدة',
            'مكة المكرمة',
            'المدينة المنورة',
            'الدمام',
            'الخبر',
            'الظهران',
            'الطائف',
            'بريدة',
            'تبوك',
            'خميس مشيط',
            'حائل',
            'الجبيل',
            'الخرج',
            'الأحساء',
            'نجران',
            'ينبع',
            'عرعر',
            'سكاكا',
            'جيزان',
            'أبها',
            'القطيف',
            'صفوى',
            'رابغ',
            'الباحة',
            'القنفذة',
            'الليث',
            'وادي الدواسر',
            'الزلفي',
            'المجمعة',
            'شقراء',
            'الدوادمي',
            'الرس',
            'عنيزة',
            'الكويت',
            'الأفلاج',
            'السليل',
            'شرورة',
            'محايل عسير',
            'بيشة',
            'الدرعية',
            'الحوية',
            'الغاط',
            'حوطة بني تميم',
            'الحريق',
        ];
    }
}
