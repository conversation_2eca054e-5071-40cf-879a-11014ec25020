<?php $__env->startSection('title', $service->name . ' - خدماتنا - موتور لاين'); ?>

<?php $__env->startSection('meta_description', Str::limit($service->description ?? 'تفاصيل خدمة ' . $service->name, 160)); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-5">
    
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="<?php echo e(route('site.home')); ?>" class="text-decoration-none">
                    <i class="fas fa-home me-1"></i>
                    الرئيسية
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="<?php echo e(route('site.services.index')); ?>" class="text-decoration-none">
                    خدماتنا
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <?php echo e($service->name); ?>

            </li>
        </ol>
    </nav>

    <div class="row">
        
        <div class="col-lg-8 mb-4">
            <div class="card brand-card shadow">
                
                <?php if($service->main_image_url): ?>
                    <img src="<?php echo e($service->main_image_url); ?>"
                         class="card-img-top"
                         alt="<?php echo e($service->name); ?>"
                         style="height: 300px; object-fit: cover;">
                <?php else: ?>
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                         style="height: 300px;">
                        <i class="fas fa-tools fa-4x text-muted"></i>
                    </div>
                <?php endif; ?>

                <div class="card-body">
                    
                    <?php if($service->category): ?>
                        <div class="mb-3">
                            <span class="badge brand-badge-secondary fs-6">
                                <i class="fas fa-tag me-1"></i>
                                <?php echo e($service->category->name); ?>

                            </span>
                        </div>
                    <?php endif; ?>

                    
                    <h1 class="card-title brand-text-primary mb-3">
                        <?php echo e($service->name); ?>

                    </h1>

                    
                    <div class="mb-4">
                        <span class="h3 brand-text-primary fw-bold">
                            <?php echo e($service->formatted_price); ?>

                        </span>
                    </div>

                    
                    <?php if($service->description): ?>
                        <div class="mb-4">
                            <h5 class="brand-text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                تفاصيل الخدمة
                            </h5>
                            <div class="text-muted lh-lg">
                                <?php echo nl2br(e($service->description)); ?>

                            </div>
                        </div>
                    <?php endif; ?>

                    
                    <div class="d-grid gap-2 d-md-flex">
                        <button type="button"
                                class="btn brand-btn-primary btn-lg flex-md-fill"
                                data-service-id="<?php echo e($service->id); ?>"
                                data-service-name="<?php echo e($service->name); ?>"
                                data-bs-toggle="modal"
                                data-bs-target="#serviceRequestModal">
                            <i class="fas fa-paper-plane me-2"></i>
                            اطلب الخدمة الآن
                        </button>
                        <a href="<?php echo e(route('site.services.index')); ?>"
                           class="btn brand-btn-outline-primary btn-lg flex-md-fill">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للخدمات
                        </a>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="col-lg-4">
            
            <div class="card brand-card shadow mb-4">
                <div class="card-header brand-bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-tag text-muted me-2"></i>
                            <strong>الفئة:</strong>
                            <?php echo e($service->category->name ?? 'غير محدد'); ?>

                        </li>
                        <li class="mb-2">
                            <i class="fas fa-money-bill-wave text-muted me-2"></i>
                            <strong>السعر:</strong>
                            <span class="brand-text-primary fw-bold"><?php echo e($service->formatted_price); ?></span>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-clock text-muted me-2"></i>
                            <strong>وقت الاستجابة:</strong>
                            خلال 24 ساعة
                        </li>
                        <li>
                            <i class="fas fa-shield-alt text-muted me-2"></i>
                            <strong>الضمان:</strong>
                            حسب نوع الخدمة
                        </li>
                    </ul>
                </div>
            </div>

            
            <?php if($relatedServices->count() > 0): ?>
                <div class="card brand-card shadow">
                    <div class="card-header brand-bg-secondary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            خدمات مشابهة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php $__currentLoopData = $relatedServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="d-flex align-items-center mb-3 <?php echo e(!$loop->last ? 'border-bottom pb-3' : ''); ?>">
                                <?php if($relatedService->main_image_url): ?>
                                    <img src="<?php echo e($relatedService->main_image_url); ?>"
                                         class="rounded me-3"
                                         alt="<?php echo e($relatedService->name); ?>"
                                         style="width: 60px; height: 60px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-tools text-muted"></i>
                                    </div>
                                <?php endif; ?>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="<?php echo e(route('site.services.show', $relatedService->id)); ?>"
                                           class="text-decoration-none brand-text-primary">
                                            <?php echo e($relatedService->name); ?>

                                        </a>
                                    </h6>
                                    <small class="text-muted"><?php echo e($relatedService->formatted_price); ?></small>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>


<?php echo $__env->make('servicemanagement::site.services._request_form', ['service' => $service], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>



<?php $__env->startPush('styles'); ?>
<style>
.breadcrumb-item + .breadcrumb-item::before {
    content: "‹";
    font-weight: bold;
}

.card-img-top {
    transition: transform 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.02);
}

.lh-lg {
    line-height: 1.8 !important;
}

@media (max-width: 768px) {
    .card-img-top {
        height: 200px !important;
    }

    .btn-lg {
        font-size: 1rem;
        padding: 0.75rem 1rem;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('site.layouts.site_layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Project\MotorLine_10\Modules/ServiceManagement\Resources/views/site/services/show.blade.php ENDPATH**/ ?>