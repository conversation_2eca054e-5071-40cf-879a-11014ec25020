@extends('site.layouts.site_layout')

@section('title', $promotion->name . ' - موتور لاين')
@section('meta_description', 'تفاصيل العرض الترويجي: ' . $promotion->name)

@section('content')
<div class="container py-5">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('site.home') }}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{{ route('site.promotions.index') }}">عروض السيارات</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ $promotion->name }}</li>
        </ol>
    </nav>

    <!-- Promotion Banner -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                @if($promotion->getFirstMediaUrl('promotion_banners'))
                    <img src="{{ $promotion->getFirstMediaUrl('promotion_banners') }}" 
                         class="card-img-top" 
                         alt="{{ $promotion->name }}"
                         style="height: 400px; object-fit: cover;">
                @endif
                
                <div class="card-body">
                    <h1 class="card-title">{{ $promotion->name }}</h1>
                    
                    @if($promotion->description)
                        <div class="card-text">
                            {!! nl2br(e($promotion->description)) !!}
                        </div>
                    @endif
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-calendar-start me-1"></i>
                                يبدأ في: {{ $promotion->start_date->format('Y/m/d') }}
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-calendar-alt me-1"></i>
                                ينتهي في: {{ $promotion->end_date->format('Y/m/d') }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cars Section -->
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">السيارات المشمولة بالعرض</h2>
            
            @if($promotion->cars->count() > 0)
                <div class="row">
                    @foreach($promotion->cars as $car)
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 shadow-sm">
                                <img src="{{ $car->getFirstMediaUrl('car_images') ?: asset('placeholder.jpg') }}" 
                                     class="card-img-top" 
                                     alt="{{ $car->full_name }}"
                                     style="height: 200px; object-fit: cover;"
                                     onerror="this.src='{{ asset('placeholder.jpg') }}'">
                                
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title">{{ $car->full_name }}</h5>
                                    
                                    <div class="mb-3">
                                        @if($car->pivot->car_offer_price)
                                            <div class="price-section">
                                                <span class="text-muted text-decoration-line-through">
                                                    {{ number_format($car->price, 0) }} ر.س
                                                </span>
                                                <br>
                                                <span class="h5 text-primary">
                                                    {{ number_format($car->pivot->car_offer_price, 0) }} ر.س
                                                </span>
                                                <span class="badge bg-success ms-2">
                                                    خصم {{ number_format($car->price - $car->pivot->car_offer_price, 0) }} ر.س
                                                </span>
                                            </div>
                                        @else
                                            <span class="h5 text-primary">
                                                {{ number_format($car->price, 0) }} ر.س
                                            </span>
                                        @endif
                                    </div>
                                    
                                    <div class="mt-auto">
                                        <a href="{{ route('site.cars.show', $car->id) }}" class="btn btn-primary w-100">
                                            <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <!-- Empty State -->
                <div class="text-center py-5">
                    <i class="fas fa-car fa-4x text-muted mb-3"></i>
                    <h3 class="text-muted">لا توجد سيارات مشمولة بهذا العرض حالياً</h3>
                    <p class="text-muted">يرجى التحقق مرة أخرى قريباً أو تصفح عروضنا الأخرى</p>
                    <a href="{{ route('site.promotions.index') }}" class="btn btn-primary mt-3">
                        <i class="fas fa-tags me-2"></i>تصفح العروض الأخرى
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
