<?php $__env->startSection('title', $promotion->name . ' - موتور لاين'); ?>
<?php $__env->startSection('meta_description', 'تفاصيل العرض الترويجي: ' . $promotion->name); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-5">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('site.home')); ?>">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('site.promotions.index')); ?>">عروض السيارات</a></li>
            <li class="breadcrumb-item active" aria-current="page"><?php echo e($promotion->name); ?></li>
        </ol>
    </nav>

    <!-- Promotion Banner -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <?php if($promotion->getFirstMediaUrl('promotion_banners')): ?>
                    <img src="<?php echo e($promotion->getFirstMediaUrl('promotion_banners')); ?>" 
                         class="card-img-top" 
                         alt="<?php echo e($promotion->name); ?>"
                         style="height: 400px; object-fit: cover;">
                <?php endif; ?>
                
                <div class="card-body">
                    <h1 class="card-title"><?php echo e($promotion->name); ?></h1>
                    
                    <?php if($promotion->description): ?>
                        <div class="card-text">
                            <?php echo nl2br(e($promotion->description)); ?>

                        </div>
                    <?php endif; ?>
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-calendar-start me-1"></i>
                                يبدأ في: <?php echo e($promotion->start_date->format('Y/m/d')); ?>

                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-calendar-alt me-1"></i>
                                ينتهي في: <?php echo e($promotion->end_date->format('Y/m/d')); ?>

                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cars Section -->
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">السيارات المشمولة بالعرض</h2>
            
            <?php if($promotion->cars->count() > 0): ?>
                <div class="row">
                    <?php $__currentLoopData = $promotion->cars; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $car): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 shadow-sm">
                                <img src="<?php echo e($car->getFirstMediaUrl('car_images') ?: asset('placeholder.jpg')); ?>" 
                                     class="card-img-top" 
                                     alt="<?php echo e($car->full_name); ?>"
                                     style="height: 200px; object-fit: cover;"
                                     onerror="this.src='<?php echo e(asset('placeholder.jpg')); ?>'">
                                
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title"><?php echo e($car->full_name); ?></h5>
                                    
                                    <div class="mb-3">
                                        <?php if($car->pivot->car_offer_price): ?>
                                            <div class="price-section">
                                                <span class="text-muted text-decoration-line-through">
                                                    <?php echo e(number_format($car->price, 0)); ?> ر.س
                                                </span>
                                                <br>
                                                <span class="h5 text-primary">
                                                    <?php echo e(number_format($car->pivot->car_offer_price, 0)); ?> ر.س
                                                </span>
                                                <span class="badge bg-success ms-2">
                                                    خصم <?php echo e(number_format($car->price - $car->pivot->car_offer_price, 0)); ?> ر.س
                                                </span>
                                            </div>
                                        <?php else: ?>
                                            <span class="h5 text-primary">
                                                <?php echo e(number_format($car->price, 0)); ?> ر.س
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="mt-auto">
                                        <a href="<?php echo e(route('site.cars.show', $car->id)); ?>" class="btn btn-primary w-100">
                                            <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <!-- Empty State -->
                <div class="text-center py-5">
                    <i class="fas fa-car fa-4x text-muted mb-3"></i>
                    <h3 class="text-muted">لا توجد سيارات مشمولة بهذا العرض حالياً</h3>
                    <p class="text-muted">يرجى التحقق مرة أخرى قريباً أو تصفح عروضنا الأخرى</p>
                    <a href="<?php echo e(route('site.promotions.index')); ?>" class="btn btn-primary mt-3">
                        <i class="fas fa-tags me-2"></i>تصفح العروض الأخرى
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('site.layouts.site_layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Project\MotorLine_10\Modules/PromotionManagement\Resources/views/site/promotions/show.blade.php ENDPATH**/ ?>