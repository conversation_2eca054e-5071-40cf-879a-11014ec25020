<?php

namespace Modules\CorporateSales\Http\Requests\Site;

use Modules\Core\Http\Requests\BaseRequest;

/**
 * طلب التحقق من صحة بيانات استعلام الشركات
 *
 * يتحقق هذا الطلب من صحة بيانات نموذج طلب مبيعات الشركات
 */
class CorporateEnquiryRequest extends BaseRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     *
     * @return bool
     */
    public function authorize()
    {
        // السماح لجميع الزوار بتقديم طلبات الشركات
        return true;
    }

    /**
     * الحصول على قواعد التحقق من صحة البيانات المطبقة على الطلب
     *
     * @return array
     */
    public function rules()
    {
        return [
            'company_name' => [
                'required',
                'string',
                'max:200',
                'min:2',
            ],
            'contact_person_name' => [
                'required',
                'string',
                'max:100',
                'min:2',
            ],
            'email' => [
                'required',
                'email',
                'max:150',
            ],
            'phone' => [
                'required',
                'string',
                'regex:/^(05|5)[0-9]{8}$/',
                'max:20',
            ],
            'request_details' => [
                'required',
                'string',
                'min:10',
                'max:2000',
            ],
            'city' => [
                'nullable',
                'string',
                'max:100',
            ],
        ];
    }

    /**
     * الحصول على رسائل الخطأ المخصصة للتحقق من صحة البيانات
     *
     * @return array
     */
    public function messages()
    {
        return [
            'company_name.required' => 'اسم الشركة مطلوب',
            'company_name.string' => 'اسم الشركة يجب أن يكون نصاً',
            'company_name.max' => 'اسم الشركة يجب ألا يتجاوز 200 حرف',
            'company_name.min' => 'اسم الشركة يجب أن يكون على الأقل حرفين',

            'contact_person_name.required' => 'اسم مسؤول الاتصال مطلوب',
            'contact_person_name.string' => 'اسم مسؤول الاتصال يجب أن يكون نصاً',
            'contact_person_name.max' => 'اسم مسؤول الاتصال يجب ألا يتجاوز 100 حرف',
            'contact_person_name.min' => 'اسم مسؤول الاتصال يجب أن يكون على الأقل حرفين',

            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'email.max' => 'البريد الإلكتروني يجب ألا يتجاوز 150 حرف',

            'phone.required' => 'رقم الجوال مطلوب',
            'phone.string' => 'رقم الجوال يجب أن يكون نصاً',
            'phone.regex' => 'رقم الجوال يجب أن يبدأ بـ 05 ويتكون من 10 أرقام',
            'phone.max' => 'رقم الجوال يجب ألا يتجاوز 20 حرف',

            'request_details.required' => 'تفاصيل الطلب مطلوبة',
            'request_details.string' => 'تفاصيل الطلب يجب أن تكون نصاً',
            'request_details.min' => 'تفاصيل الطلب يجب أن تكون على الأقل 10 أحرف',
            'request_details.max' => 'تفاصيل الطلب يجب ألا تتجاوز 2000 حرف',

            'city.string' => 'المدينة يجب أن تكون نصاً',
            'city.max' => 'المدينة يجب ألا تتجاوز 100 حرف',
        ];
    }

    /**
     * الحصول على أسماء الحقول المخصصة
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'company_name' => 'اسم الشركة',
            'contact_person_name' => 'اسم مسؤول الاتصال',
            'email' => 'البريد الإلكتروني',
            'phone' => 'رقم الجوال',
            'request_details' => 'تفاصيل الطلب',
            'city' => 'المدينة',
        ];
    }
}
